<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Queue Admin</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        header h1 {
            color: white;
            margin: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .card-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .start-worker-btn {
            background-color: #2ecc71;
            margin-left: 10px;
        }
        .start-worker-btn:hover {
            background-color: #27ae60;
        }
        .run-once-btn {
            background-color: #f39c12;
            margin-left: 10px;
        }
        .run-once-btn:hover {
            background-color: #d35400;
        }
        .stop-worker-btn {
            background-color: #e74c3c;
            margin-left: 10px;
        }
        .stop-worker-btn:hover {
            background-color: #c0392b;
        }
        .output {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
            margin-left: 5px;
        }
        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #555;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            top: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 14px;
            line-height: 1.4;
        }
        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
        .badge {
            display: inline-block;
            padding: 3px 7px;
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 10px;
            background-color: #3498db;
        }
        .readme {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-top: 30px;
        }
        .readme pre {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
        }
        .readme code {
            font-family: monospace;
        }
        .workflow-diagram {
            padding: 20px;
        }
        .workflow-steps {
            counter-reset: step;
            padding-left: 0;
        }
        .workflow-steps li {
            list-style-type: none;
            position: relative;
            margin-bottom: 30px;
            padding: 15px 20px 15px 70px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .workflow-steps li:before {
            counter-increment: step;
            content: counter(step);
            position: absolute;
            left: 20px;
            top: 15px;
            background-color: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            font-weight: bold;
        }
        .workflow-steps li strong {
            display: block;
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 18px;
        }
        .workflow-steps li p {
            margin-top: 0;
        }
        .workflow-steps li code {
            background-color: #e9ecef;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .task-table th {
            background-color: #2c3e50;
            color: white;
            text-align: left;
            padding: 10px;
        }
        .task-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .task-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .task-table .task-name {
            font-weight: bold;
            width: 60%;
        }
        .task-table .count {
            text-align: center;
            font-weight: bold;
        }
        .task-table .count-zero {
            color: #95a5a6;
        }
        .task-table .count-nonzero {
            color: #2980b9;
        }
        .task-table .count-success {
            color: #27ae60;
        }
        .task-table .count-error {
            color: #c0392b;
        }
        .task-table .loading-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-running {
            background-color: #2ecc71;
        }
        .status-stopped {
            background-color: #e74c3c;
        }
        #workerStatus, #workerTabStatus {
            font-weight: bold;
            margin-left: 10px;
        }
        .worker-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .worker-mode-section {
            flex: 1;
        }
        .worker-buttons {
            display: flex;
            gap: 10px;
        }
        .worker-status-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        .run-worker-btn {
            background-color: #2ecc71;
        }
        .run-worker-btn:hover {
            background-color: #27ae60;
        }
        .stop-worker-btn {
            background-color: #e74c3c;
        }
        .stop-worker-btn:hover {
            background-color: #c0392b;
        }
        .success-message {
            color: #2ecc71;
            font-weight: bold;
        }
        .failure-message {
            color: #e74c3c;
            font-weight: bold;
        }
        .console-log {
            max-height: 400px;
            overflow-y: auto;
            background-color: #1e1e1e;
            color: #f0f0f0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.4;
            font-size: 14px;
        }
        .console-log .log-entry {
            margin-bottom: 4px;
            border-bottom: 1px solid #333;
            padding-bottom: 4px;
        }
        .console-log .timestamp {
            color: #888;
            margin-right: 8px;
        }
        .console-log .task-id {
            color: #569cd6;
            font-weight: bold;
            margin-right: 8px;
        }
        .console-log .task-type {
            color: #4ec9b0;
            font-weight: bold;
            margin-right: 8px;
        }
        .console-log .success {
            color: #6a9955;
        }
        .console-log .error {
            color: #f14c4c;
        }
        .console-log .warning {
            color: #dcdcaa;
        }
        .console-log .info {
            color: #9cdcfe;
        }
        .console-log .processing {
            color: #ce9178;
        }
        .console-log .completed {
            color: #6a9955;
            font-weight: bold;
        }
        .console-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
        }
        .clear-console-btn {
            background-color: #7f8c8d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        .clear-console-btn:hover {
            background-color: #95a5a6;
        }
        .auto-scroll-label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #ddd;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        /* Enhanced Informed Tab Styles */
        .workflow-section {
            margin-bottom: 30px;
        }

        .step-group {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .step-group h3 {
            margin-top: 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .button-row {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .button-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .step-btn {
            min-width: 180px;
            padding: 10px 16px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .step-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .workflow-btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .workflow-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .info-box {
            margin: 15px 0;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .success-highlight {
            background: #e8f5e8;
            border-left-color: #27ae60;
            color: #27ae60;
        }

        .warning-highlight {
            background: #fff3cd;
            border-left-color: #f39c12;
            color: #856404;
        }

        .enhanced-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }

        .step-output {
            background: #f8f9fa;
            border-left: 3px solid #3498db;
            padding: 10px;
            margin-top: 10px;
            border-radius: 0 4px 4px 0;
        }

        .output-section {
            margin-top: 20px;
        }

        .card-header.gradient-blue {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .card-header.gradient-green {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .card-header.gradient-orange {
            background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
            color: white;
        }

        .card-header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 300px;
            background-color: #555;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -150px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <header>
        <h1>Task Queue Admin Dashboard</h1>
        <p>Manage and monitor your task queue system</p>
    </header>

    <div class="tabs">
        <div class="tab active" data-tab="dashboard">Dashboard</div>
        <div class="tab" data-tab="tasks">Tasks</div>
        <div class="tab" data-tab="reconcile">Reconcile</div>
        <div class="tab" data-tab="worker">Worker</div>
        <div class="tab" data-tab="flow">Workflow</div>
        <div class="tab" data-tab="informed">Informed</div>
        <div class="tab" data-tab="documentation">Documentation</div>
        <div class="tab" data-tab="amazon">Amazon FBA</div>
    </div>

    <div id="dashboard" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h2>System Overview</h2>
            </div>
            <div>
                <p><strong>Worker Status:</strong>
                    <span class="status-indicator status-stopped"></span>
                    <span id="workerStatus">Stopped</span>
                    <button id="refreshStatus">Refresh</button>
                    <button id="dashboardRunWorkerOnce" class="run-once-btn">Run Worker Once</button>
                </p>
                <p><strong>Pending Tasks:</strong> <span id="pendingTasksCount">Loading...</span></p>
                <p><strong>Future Scheduled Tasks:</strong> <span id="futureTasksCount">Loading...</span></p>
                <p><strong>Last Run:</strong> <span id="lastRunTime">Unknown</span></p>
            </div>

            <div class="card-header">
                <h3>Task Queue Status by Workflow Step</h3>
            </div>
            <div id="taskWorkflowTable">
                <table class="task-table">
                    <thead>
                        <tr>
                            <th>Task Type</th>
                            <th>Pending</th>
                            <th>Future</th>
                            <th>Completed (30m)</th>
                            <th>Errors (30m)</th>
                        </tr>
                    </thead>
                    <tbody id="taskTableBody">
                        <tr>
                            <td colspan="5" class="loading-message">Loading task statistics...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="card-header">
                <h3>Live Console Log</h3>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Real-time output from the worker process.
                    </span>
                </div>
            </div>
            <div class="console-controls">
                <div>
                    <button id="clearConsoleBtn" class="clear-console-btn">Clear Console</button>
                    <select id="logFilterSelect" title="Filter log entries">
                        <option value="all">All Messages</option>
                        <option value="error">Errors Only</option>
                        <option value="success">Success Only</option>
                        <option value="processing">Processing Only</option>
                    </select>
                    <input type="text" id="taskTypeFilter" placeholder="Filter by task type..." style="width: 180px;">
                </div>
                <div>
                    <label class="auto-scroll-label">
                        <input type="checkbox" id="autoScrollCheckbox" checked>
                        Auto-scroll
                    </label>
                    <label class="auto-scroll-label" style="margin-left: 10px;">
                        <input type="checkbox" id="showTimestampsCheckbox" checked>
                        Show Timestamps
                    </label>
                </div>
            </div>
            <div id="dashboardConsoleLog" class="output console-log"></div>
        </div>
    </div>

    <div id="tasks" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Enqueue Image Verification Task</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        This tool creates a new task in the t_task_queue table to verify an image.
                        The task will be processed by the worker when its scheduled time is reached.
                    </span>
                </div>
            </div>
            <form id="enqueueForm">
                <div class="form-group">
                    <label for="imageId">Image ID <span class="badge">Required</span></label>
                    <input type="number" id="imageId" name="imageId" required placeholder="Enter the t_images.id value">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            The ID of the image record in the t_images table that you want to verify.
                            This will be used in the task payload as {"id": 123456}.
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="delayMinutes">Delay (minutes)</label>
                    <input type="number" id="delayMinutes" name="delayMinutes" value="0" min="0" placeholder="0 = immediate execution">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Number of minutes to delay task execution. Use 0 for immediate execution.
                            Tasks will only be processed when their scheduled time is reached.
                        </span>
                    </div>
                </div>
                <button type="submit">Enqueue Task</button>
            </form>
            <div id="enqueueOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>View Pending Tasks</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        View and manage tasks currently in the queue.
                    </span>
                </div>
            </div>
            <button id="viewTasksBtn">Refresh Task List</button>
            <div id="tasksOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>Import Discs from Google Sheets</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Import disc data from a Google Sheets document into the t_discs table with validation and foreign key mapping.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Make sure your Google Sheet is publicly viewable</li>
                    <li>The sheet should have columns: shipment_id, mps_id, color_modifier, color, weight_mfg, weight_scale, grade, description, location, notes, new_id</li>
                    <li>Column mapping: weight_scale → weight, weight_mfg → weight_mfg</li>
                    <li>The 'new_id' column will be populated with the new t_discs.id values after import</li>
                    <li><strong>Most fields are required - any blank required field will mark the row as invalid</strong></li>
                </ol>
                <p><strong>Required Fields:</strong> shipment_id, mps_id, color, weight_mfg, weight_scale, grade, description, location</p>
                <p><strong>Optional Fields:</strong> color_modifier, notes, new_id</p>
                <p><strong>Validation:</strong> shipment_id must exist in t_shipments, mps_id must exist in t_mps, color must match t_colors.color</p>
            </div>
            <form id="importDiscsForm">
                <div class="form-group">
                    <label for="googleSheetsUrl">Google Sheets URL <span class="badge">Required</span></label>
                    <input type="url" id="googleSheetsUrl" name="googleSheetsUrl" required
                           placeholder="https://docs.google.com/spreadsheets/d/YOUR_SHEET_ID/edit#gid=0"
                           style="width: 100%;">
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Paste the full Google Sheets URL. The sheet must be publicly viewable for the import to work.
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="validateOnly">
                        <input type="checkbox" id="validateOnly" name="validateOnly">
                        Validate Only (don't import)
                    </label>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Check this to only validate the data without importing. Use this to check for errors first.
                        </span>
                    </div>
                </div>
                <button type="submit">Import Discs from Google Sheets</button>
            </form>
            <div id="importDiscsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Delete Wrong Veeqo Records</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Deletes Veeqo product variants with variant titles containing " (D#" through the Veeqo API.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Warning:</strong> This will permanently delete product variants in Veeqo that have variant titles containing " (D#".</p>
                <p>These are typically duplicate records that were created incorrectly and should be removed.</p>
                <p>This action cannot be undone. Please make sure you have a recent backup of your Veeqo data.</p>
            </div>
            <div class="form-group">
                <label for="deleteVeeqoLimit">Number of records to delete:</label>
                <select id="deleteVeeqoLimit" class="form-control">
                    <option value="1">Delete 1 record (test)</option>
                    <option value="10">Delete 10 records</option>
                    <option value="">Delete all records</option>
                </select>
            </div>
            <button id="deleteWrongVeeqoRecordsBtn" class="run-once-btn">Delete Wrong Veeqo Records</button>
            <div id="deleteWrongVeeqoRecordsOutput" class="output" style="display: none;"></div>
        </div>


    </div>

    <div id="reconcile" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Reconcile OSL Stats Into t_inv_osl</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates t_inv_osl records with correct quantity counts from the calculated stats table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process calls the <code>fn_reconcile_osl_inventory</code> function which updates <code>t_inv_osl</code> records with correct quantity counts from the <code>v_stats_by_osl</code> view.</p>
                <p>The function processes records in batches of 50 and will continue running until all discrepancies are resolved. These updates to <code>t_inv_osl</code> will trigger task queue entries to update Veeqo quantities.</p>
            </div>
            <button id="reconcileOslStatsBtn" class="run-once-btn">Reconcile OSL Stats</button>
            <div id="reconcileOslStatsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile SDAsin Stats Into t_inv_sdasin</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates t_inv_sdasin records with correct quantity counts from the calculated stats table.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process calls the <code>fn_reconcile_sdasin_inventory</code> function which updates <code>t_inv_sdasin</code> records with correct quantity counts from the <code>v_stats_by_sdasin</code> view.</p>
                <p>The function processes records in batches of 50 and will continue running until all discrepancies are resolved. These updates to <code>t_inv_sdasin</code> will trigger task queue entries to update Veeqo quantities.</p>
            </div>
            <button id="reconcileSdasinStatsBtn" class="run-once-btn">Reconcile SDAsin Stats</button>
            <div id="reconcileSdasinStatsOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Import Veeqo Sellables</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Truncates the imported_table_veeqo_sellables_export table and runs the import_veeqo_sellables.js script.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>IMPORTANT STEP:</strong> Before clicking the button below, you must manually download the CSV export from Veeqo and save it to:</p>
                <p><code>C:\Users\<USER>\supabase_project\data\external data\veeqo_sellables_export.csv</code></p>
                <p>To download the export from Veeqo:</p>
                <ol>
                    <li>Log in to Veeqo</li>
                    <li>Go to Products</li>
                    <li>Click "Export" button</li>
                    <li>Download the CSV file</li>
                    <li>Save it to the exact path specified above</li>
                </ol>
                <p>After saving the file to the correct location, click the button below to import the data.</p>
            </div>
            <button id="importVeeqoSellablesBtn" class="run-worker-btn">Import Veeqo Sellables</button>
            <div id="importVeeqoSellablesOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile Discs to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Runs the reconcileDToVeeqo.js script to update discs with inventory discrepancies in Veeqo.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this script, please browse through the <code>v_reconcile_d_to_veeqo</code> view to review the discrepancies that will be processed.</p>
                <p>This script will update Veeqo quantities to 0 for sold discs that still show inventory in Veeqo.</p>
            </div>
            <button id="reconcileDToVeeqoBtn" class="run-once-btn">Reconcile Discs to Veeqo</button>
            <div id="reconcileDToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile RPRO to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Refreshes the reconcile_rpro_counts_to_veeqo table to compare RPRO and Veeqo quantities.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Description:</strong> This process refreshes the <code>reconcile_rpro_counts_to_veeqo</code> table that compares quantities between RPRO (ivqtylaw) and Veeqo (total_qty).</p>
                <p>After running this, you can browse the <code>v_reconcile_rpro_counts_to_veeqo</code> view to see the discrepancies.</p>
            </div>
            <button id="reconcileRproToVeeqoBtn" class="run-once-btn">Refresh RPRO to Veeqo Reconciliation</button>
            <div id="reconcileRproToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Update Veeqo Quantities from RPRO</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Updates Veeqo product quantities to match RPRO quantities for records with discrepancies.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Important:</strong> This process will update Veeqo product quantities based on RPRO quantities for all records with discrepancies in the <code>v_reconcile_rpro_counts_to_veeqo</code> view.</p>
                <p>The following rules will be applied:</p>
                <ul>
                    <li>If RPRO quantity is negative, Veeqo quantity will be set to 0</li>
                    <li>If RPRO quantity has decimals, it will be rounded down (Veeqo doesn't support decimal quantities)</li>
                    <li>Otherwise, Veeqo quantity will be set equal to RPRO quantity</li>
                </ul>
                <p><strong>Note:</strong> It's recommended to run the "Refresh RPRO to Veeqo Reconciliation" process first to ensure you're working with the latest data.</p>
            </div>
            <button id="updateVeeqoFromRproBtn" class="run-once-btn">Update Veeqo from RPRO</button>
            <div id="updateVeeqoFromRproOutput" class="output" style="display: none;"></div>
        </div>



        <div class="card">
            <div class="card-header">
                <h2>Reconcile OSLs to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds OSLs with inventory discrepancies in Veeqo and enqueues tasks to update their quantities.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this script, please browse through the <code>v_reconcile_osl_to_veeqo</code> view to review the discrepancies that will be processed.</p>
                <p>This will enqueue tasks to update OSL quantities in Veeqo to match our local quantities.</p>
            </div>
            <button id="reconcileOSLToVeeqoBtn" class="run-once-btn">Reconcile OSLs to Veeqo</button>
            <div id="reconcileOSLToVeeqoOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile SDAsins to Veeqo</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        place holder
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #f39c12; margin-bottom: 15px;">
                <p>place holder</p>
            </div>
            <button id="placeholder" class="run-once-btn">place holder</button>
            <div id="placeholder" class="output" style="display: none;"></div>
        </div>


        <div class="card">
            <div class="card-header">
                <h2>Download Shopify Matrixify Export</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Instructions for downloading the Matrixify export file from Shopify.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #9b59b6; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before proceeding with Shopify reconciliation, you need to download the latest Matrixify export file and save it to:</p>
                <p><code>C:\Users\<USER>\supabase_project\data\external data\shopify_matrixify_export_dg.csv</code></p>
                <p>To download the Matrixify export from Shopify:</p>
                <ol>
                    <li>Log in to your Shopify admin</li>
                    <li>Go to Apps > Matrixify</li>
                    <li>Select the "Export" tab</li>
                    <li>Choose the appropriate export template</li>
                    <li>Click "Export" and wait for the export to complete</li>
                    <li>Download the CSV file</li>
                    <li>Save it to the exact path specified above</li>
                </ol>
                <p>After saving the file to the correct location, you can proceed with the Shopify reconciliation tasks below.</p>
            </div>
            <button id="importShopifyMatrixifyBtn" class="run-once-btn">Import Shopify Matrixify Export to imported_table_shopify_products_dz</button>
            <div id="importShopifyMatrixifyOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Reconcile Sold Discs on Shopify</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds sold discs still showing on Shopify and enqueues tasks to set their quantities to 0.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 15px;">
                <p><strong>Important:</strong> Before running this, please browse through the <code>v_reconcile_d_to_shopify</code> view to review the discs with the issue "Sold disc still showing up on Shopify."</p>
                <p>This will enqueue tasks to set the Shopify inventory to 0 for sold discs. You can then process these tasks one at a time from the task queue.</p>
            </div>
            <button id="enqueueSoldDiscsShopifyBtn" class="run-once-btn">Enqueue Sold Discs Shopify Tasks</button>
            <div id="enqueueSoldDiscsShopifyOutput" class="output" style="display: none;"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Generate OSL Fields for Null G_Code</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Finds OSLs with null g_code and enqueues tasks to generate the fields.
                    </span>
                </div>
            </div>
            <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 15px;">
                <p><strong>Info:</strong> This will find OSLs where g_code is null and enqueue tasks to generate the fields.</p>
                <p>Each task will be processed by the worker to update the OSL record with generated fields.</p>
            </div>
            <div class="form-group">
                <label for="oslBatchSize">Batch Size</label>
                <input type="number" id="oslBatchSize" name="oslBatchSize" value="100" min="1" max="1000" placeholder="Number of OSLs to process">
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        Maximum number of OSLs to process in one batch. Use a smaller number for quicker processing.
                    </span>
                </div>
            </div>
            <button id="enqueueGenerateOslFieldsBtn" class="run-once-btn">Enqueue Generate OSL Fields Tasks</button>
            <div id="enqueueGenerateOslFieldsOutput" class="output" style="display: none;"></div>
        </div>
    </div>

    <div id="worker" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Task Queue Worker</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        The worker processes pending tasks from the queue.
                        It can be run once or continuously as a daemon.
                    </span>
                </div>
            </div>
            <div class="worker-controls">
                <div class="worker-mode-section">
                    <div class="form-group">
                        <label for="workerMode">Worker Mode</label>
                        <select id="workerMode">
                            <option value="once">Run Once</option>
                            <option value="daemon">Run as Daemon</option>
                        </select>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                "Run Once" processes pending tasks once and exits.
                                "Run as Daemon" continuously checks for new tasks every 15 seconds, processing up to 100 tasks per run.
                            </span>
                        </div>
                    </div>
                </div>
                <div class="worker-buttons">
                    <button id="runWorkerBtn" class="run-worker-btn">Run Worker</button>
                    <button id="stopWorkerBtn" class="stop-worker-btn" style="display: none;">Stop Worker</button>
                </div>
            </div>
            <div class="worker-status-section">
                <p><strong>Worker Status:</strong>
                    <span class="status-indicator status-stopped"></span>
                    <span id="workerTabStatus">Stopped</span>
                </p>
                <p><strong>Last Run:</strong> <span id="workerTabLastRunTime">Unknown</span></p>
            </div>
            <div id="workerOutput" class="output" style="display: none;"></div>
        </div>
    </div>

    <div id="flow" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Task Queue Workflows</h2>
                <div class="tooltip">ⓘ
                    <span class="tooltiptext">
                        This section shows all the task types handled by the worker, including the disc publishing workflow and additional task types for field generation, matching, and inventory management.
                    </span>
                </div>
            </div>
            <div class="workflow-diagram">
                <h3>Workflow Steps</h3>
                <ol class="workflow-steps">
                    <li>
                        <strong>Image File Name Update</strong>
                        <p>When a disc's image_file_name is updated in t_discs, two tasks are created:</p>
                        <ul>
                            <li><code>verify_disc_image</code> - Verifies the new image</li>
                            <li><code>clear_disc_verification</code> - Clears previous verification data</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Image Verification</strong>
                        <p>The worker processes the <code>verify_disc_image</code> task:</p>
                        <ul>
                            <li>Checks if the image is accessible</li>
                            <li>Updates t_discs.image_verified and related fields</li>
                            <li>If successful, triggers the next step</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Readiness Check</strong>
                        <p>When image_verified is set to TRUE, a <code>check_if_disc_is_ready</code> task is created:</p>
                        <ul>
                            <li>Checks if all required fields are not null</li>
                            <li>Checks if ready_button is TRUE</li>
                            <li>Checks if image_verified is TRUE</li>
                            <li>Checks if shopify_uploaded_at is NULL</li>
                            <li>If all conditions are met, sets t_discs.ready_new to TRUE</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Publishing Check</strong>
                        <p>When ready_new is set to TRUE, a <code>check_if_disc_ready_to_publish</code> task is created:</p>
                        <ul>
                            <li>Checks if the disc is in v_todo_discs</li>
                            <li>If not, creates a <code>publish_disc</code> task</li>
                            <li>If it is, updates t_discs with a note</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Publishing to Shopify</strong>
                        <p>The worker processes the <code>publish_disc</code> task:</p>
                        <ul>
                            <li>Runs the publishProductDisc.js script</li>
                            <li>Updates t_discs.shopify_uploaded_at when successful</li>
                            <li>This completes the workflow</li>
                        </ul>
                    </li>
                </ol>

                <h3>Additional Task Types</h3>
                <ol class="workflow-steps">
                    <li>
                        <strong>Field Generation Tasks</strong>
                        <p>Tasks that generate and update fields in the database:</p>
                        <ul>
                            <li><code>generate_disc_title_pull_and_handle</code> - Updates t_discs.g_title, t_discs.g_pull, and t_discs.g_handle</li>
                            <li><code>generate_mps_fields</code> - Updates t_mps.g_handle, t_mps.g_flight_numbers, t_mps.g_code, t_mps.g_blurb_with_link, and t_mps.g_seo_metafield_description</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Matching and Inventory Tasks</strong>
                        <p>Tasks that handle disc matching and inventory management:</p>
                        <ul>
                            <li><code>set_disc_carry_cost</code> - Calculates and updates t_discs.carrying_cost based on order cost and shipping multiplier</li>
                            <li><code>match_disc_to_asins</code> - Matches discs to SDASINs based on MPS, weight, and color</li>
                            <li><code>match_disc_to_osl</code> - Matches discs to order sheet lines and updates inventory counts</li>
                            <li><code>plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload</code> - Finds MPS records with matching plastic_id and creates tasks for each one</li>
                            <li><code>mps_price_verified_try_upload_osls</code> - Creates tasks for already uploaded OSLs and schedules tasks for OSLs that need uploading</li>
                            <li><code>mps_price_verified_osl_uploaded_look_for_discs</code> - Sets ready_new to FALSE for discs with matching order_sheet_line_id that are ready to publish</li>
                            <li><code>toggle_osl_ready_button</code> - Toggles ready_button for OSLs to trigger the upload process</li>
                            <li><code>reconcile_clear_count_from_shopify_for_sold_disc</code> - Sets Shopify inventory to zero for sold discs still showing on Shopify</li>
                            <li><code>update_osl_after_publish</code> - Updates OSL record after successful publishing to Shopify</li>
                        </ul>
                    </li>
                    <li>
                        <strong>Legacy Image Tasks</strong>
                        <p>Tasks that handle image records in the t_images table:</p>
                        <ul>
                            <li><code>verify_t_images_image</code> - Verifies images in the t_images table</li>
                            <li><code>insert_new_t_images_record</code> - Creates a new record in the t_images table</li>
                            <li><code>delete_t_images_record</code> - Deletes a record from the t_images table</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>
    </div>

    <div id="informed" class="tab-content">
        <!-- Main Workflow Section -->
        <div class="card" style="border: 2px solid #e67e22; background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);">
            <div class="card-header gradient-orange">
                <h2>🚀 Complete Informed Workflow</h2>
                <p>End-to-end automation for Informed Repricer management</p>
            </div>
            <div class="card-body">
                <div class="workflow-section">
                    <div class="info-box" style="background: #fff3cd; border-left-color: #f39c12; padding: 15px; margin-bottom: 20px;">
                        <h3>🔄 Full Automation Process</h3>
                        <p><strong>Complete 3-phase workflow:</strong> Download reports → Import data → Upload pricing</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>Phase 1:</strong> Download latest reports from Informed API</li>
                            <li><strong>Phase 2:</strong> Import reports into Supabase tables</li>
                            <li><strong>Phase 3:</strong> Generate and upload pricing data with REAL calculations</li>
                        </ul>
                        <p><strong>📅 Scheduled:</strong> Runs automatically at 6:30 AM, 12:30 PM, and 6:30 PM CST</p>
                    </div>

                    <div style="text-align: center; margin: 20px 0;">
                        <button id="runCompleteWorkflowBtn" class="run-worker-btn" style="background: linear-gradient(135deg, #e67e22 0%, #d35400 100%); font-size: 16px; padding: 12px 24px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                            ⚡ Run Complete Workflow
                        </button>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                Runs the complete end-to-end workflow: Downloads reports from Informed, imports them to Supabase, then uploads pricing data back to Informed. This is the full 3-step process that should be scheduled 3 times per day.
                            </span>
                        </div>
                    </div>
                    <div id="runCompleteWorkflowOutput" class="enhanced-output" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Data Management Section -->
        <div class="card">
            <div class="card-header gradient-blue">
                <h2>📊 Data Management</h2>
                <p>Download and import Informed reports</p>
            </div>
            <div class="card-body">
                <div class="info-box" style="background: #e3f2fd; border-left-color: #2196f3; padding: 15px; margin-bottom: 20px;">
                    <h4>📋 Report Types</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>All Fields</strong> → <code>it_infor_all_fields</code></li>
                        <li><strong>Competition Landscape</strong> → <code>it_infor_competition_landscape</code></li>
                        <li><strong>No Buy Box</strong> → <code>it_infor_no_buy_box</code></li>
                    </ul>
                </div>

                <div class="step-group">
                    <h3>📊 Report Status</h3>
                    <div id="informedReportStatus" class="form-group" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <p>Loading report status...</p>
                    </div>
                </div>

                <div class="step-group">
                    <h3>📥 Download & Import</h3>
                    <div class="button-row">
                        <div class="button-group">
                            <button id="downloadInformedReportsBtn" class="run-once-btn step-btn">📥 Download Reports</button>
                            <small>Get latest data from Informed</small>
                            <div class="form-group" style="margin-top: 10px;">
                                <label for="maxRetries">Max Retries:</label>
                                <input type="number" id="maxRetries" value="30" min="1" max="100" style="width: 60px;">
                                <label for="retryInterval" style="margin-left: 10px;">Retry Interval (seconds):</label>
                                <input type="number" id="retryInterval" value="10" min="5" max="60" style="width: 60px;">
                                <div class="tooltip" style="margin-left: 5px;">ⓘ
                                    <span class="tooltiptext">
                                        Max Retries: Maximum number of times to check if a report is ready.<br>
                                        Retry Interval: Time to wait between status checks in seconds.
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="button-group">
                            <button id="importInformedReportsBtn" class="run-once-btn step-btn">📋 Import Reports</button>
                            <small>Process into Supabase tables</small>
                        </div>

                        <div class="button-group">
                            <button id="runFullInformedProcessBtn" class="run-worker-btn workflow-btn">🔄 Download + Import</button>
                            <small>Combined workflow</small>
                            <div class="form-group" style="margin-top: 10px;">
                                <label for="fullProcessMaxRetries">Max Retries:</label>
                                <input type="number" id="fullProcessMaxRetries" value="30" min="1" max="100" style="width: 60px;">
                                <label for="fullProcessRetryInterval" style="margin-left: 10px;">Retry Interval (seconds):</label>
                                <input type="number" id="fullProcessRetryInterval" value="10" min="5" max="60" style="width: 60px;">
                                <div class="tooltip" style="margin-left: 5px;">ⓘ
                                    <span class="tooltiptext">
                                        Max Retries: Maximum number of times to check if a report is ready.<br>
                                        Retry Interval: Time to wait between status checks in seconds.
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <div id="downloadInformedReportsOutput" class="step-output" style="display: none;"></div>
                    <div id="importInformedReportsOutput" class="step-output" style="display: none;"></div>
                    <div id="runFullInformedProcessOutput" class="step-output" style="display: none;"></div>
                </div>

                <div class="step-group">
                    <h3>⏰ Scheduler Status</h3>
                    <div id="informedSchedulerStatus" class="form-group" style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                        <p>Loading scheduler status...</p>
                    </div>

                    <div class="button-row">
                        <button id="enableInformedSchedulerBtn" class="run-worker-btn">✅ Enable Scheduler</button>
                        <button id="disableInformedSchedulerBtn" class="stop-worker-btn">❌ Disable Scheduler</button>
                    </div>
                    <div id="informedSchedulerOutput" class="step-output" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Pricing Upload Section -->
        <div class="card">
            <div class="card-header gradient-green">
                <h2>💰 Pricing Upload</h2>
                <p>Generate and upload pricing data to Informed</p>
            </div>
            <div class="card-body">
                <div class="info-box success-highlight" style="margin-bottom: 20px;">
                    <h4>📈 Pricing Process Overview</h4>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>Truncate:</strong> Clear old pricing data from tu_informed table</li>
                        <li><strong>Fill:</strong> Populate with fresh data using REAL pricing calculations</li>
                        <li><strong>Export:</strong> Generate CSV file formatted for Informed API</li>
                        <li><strong>Upload:</strong> Send pricing data to Informed Repricer</li>
                    </ol>
                    <p style="margin: 10px 0 0 0; font-style: italic; color: #27ae60;">
                        ✅ Now using REAL MAP prices from your MPS and plastics data (no more hardcoded values!)
                    </p>
                </div>

                <div class="step-group">
                    <h3>🔧 Individual Steps</h3>
                    <div class="button-grid">
                        <div class="button-group">
                            <button id="truncateTuInformedBtn" class="run-once-btn step-btn">🗑️ 1. Truncate</button>
                            <small>Clear old data</small>
                        </div>

                        <div class="button-group">
                            <button id="fillTuInformedBtn" class="run-once-btn step-btn">📊 2. Fill Data</button>
                            <small>Real pricing calculations</small>
                        </div>

                        <div class="button-group">
                            <button id="exportCsvBtn" class="run-once-btn step-btn">📄 3. Export CSV</button>
                            <small>Generate upload file</small>
                        </div>

                        <div class="button-group">
                            <button id="uploadCsvBtn" class="run-once-btn step-btn">🚀 4. Upload</button>
                            <small>Send to Informed</small>
                        </div>
                    </div>
                </div>

                <div class="step-group" style="margin-top: 30px;">
                    <h3>⚡ Complete Upload Workflow</h3>
                    <div style="text-align: center;">
                        <button id="runUploadWorkflowBtn" class="run-worker-btn" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%); font-size: 16px; padding: 12px 24px; border-radius: 8px;">
                            🔄 Run All Upload Steps
                        </button>
                        <div class="tooltip">ⓘ
                            <span class="tooltiptext">
                                Runs all 4 upload steps in sequence: Truncate → Fill → Export → Upload. Uses optimized real pricing calculations.
                            </span>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <div id="truncateTuInformedOutput" class="step-output" style="display: none;"></div>
                    <div id="fillTuInformedOutput" class="step-output" style="display: none;"></div>
                    <div id="exportCsvOutput" class="step-output" style="display: none;"></div>
                    <div id="uploadCsvOutput" class="step-output" style="display: none;"></div>
                    <div id="runUploadWorkflowOutput" class="step-output" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="documentation" class="tab-content">
        <div class="readme">
            <h2>Documentation</h2>
            <div id="readmeContent"></div>
        </div>
    </div>

    <div id="amazon" class="tab-content">
        <div class="container mt-4">
            <h1>Amazon FBA Data Management</h1>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Import Monthly Inventory Ledger Summary</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Imports Amazon Inventory Ledger Summary reports (.txt files) into the database.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #17a2b8; margin-bottom: 15px;">
                        <p><strong>IMPORTANT:</strong> Before running the import, make sure you have:</p>
                        <ol>
                            <li>Downloaded the "Monthly Inventory Ledger Summary" report from Amazon Seller Central</li>
                            <li>Saved the file as "Amazon Inventory Ledger Summary YYYY-MM.txt" (e.g., "Amazon Inventory Ledger Summary 2025-04.txt")</li>
                            <li>Placed the file in: <code>C:\Users\<USER>\supabase_project\data\external data</code></li>
                        </ol>
                    </div>

                    <p><strong>What this process does:</strong></p>
                    <ul>
                        <li>Scans the data directory for all Amazon Inventory Ledger Summary files</li>
                        <li>Extracts the date (YYYY-MM) from each filename</li>
                        <li>Imports each file into the <code>it_amaz_monthly_inventory_ledger_summary</code> table</li>
                        <li>Logs the import in the task queue</li>
                    </ul>

                    <p><strong>Note:</strong> This process appends data to the existing table. It does not delete or replace existing records.</p>

                    <button id="run-amazon-import" class="btn btn-primary">
                        Run Amazon FBA Import
                    </button>
                    <div id="amazon-import-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Refresh FBA Carrying Cost Data</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Refreshes the materialized view that calculates average carrying costs for FBA inventory valuation.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #28a745; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Refreshes the <code>mv_sdasin_avg_carrying_cost_fba</code> materialized view</li>
                            <li>Recalculates average carrying costs for all SDAINs based on current disc data</li>
                            <li>Uses priority logic: unsold discs → FBM sold discs → all sold discs → $5.00 default</li>
                            <li>Updates the cached data used for FBA inventory valuation reports</li>
                        </ul>
                    </div>

                    <p><strong>When to refresh:</strong></p>
                    <ul>
                        <li>After importing new disc data or updating carrying costs</li>
                        <li>Before generating monthly FBA inventory value reports</li>
                        <li>When you notice carrying cost calculations seem outdated</li>
                    </ul>

                    <p><strong>Note:</strong> This process may take a few minutes for large datasets as it recalculates costs for all SDAINs.</p>

                    <button id="refresh-fba-carrying-costs" class="btn btn-success">
                        Refresh FBA Carrying Costs
                    </button>
                    <div id="fba-carrying-costs-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Generate Monthly FBA Inventory Value Report</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Calculates and stores monthly FBA inventory valuation based on Amazon ledger data and carrying costs.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Finds the most recent month from Amazon Inventory Ledger Summary data</li>
                            <li>Matches Amazon SKUs (MSKU) to your SDAINs via <code>fba_sku</code> field</li>
                            <li>Uses FBA carrying costs from the materialized view</li>
                            <li>Calculates total units, weighted average cost, and total inventory value</li>
                            <li>Stores results in <code>rpt_amaz_monthly_fba_inventory_value</code> table</li>
                            <li>Processes data in chunks of 1000 records to handle large datasets</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon Inventory Ledger Summary data must be imported</li>
                            <li>FBA carrying costs materialized view should be refreshed</li>
                            <li>SDAINs must have <code>fba_sku</code> values that match Amazon MSKUs</li>
                        </ol>
                    </div>

                    <p><strong>When to run:</strong></p>
                    <ul>
                        <li>After importing new Amazon Inventory Ledger Summary data</li>
                        <li>At month-end to generate inventory valuation reports</li>
                        <li>When you need updated FBA inventory value calculations</li>
                    </ul>

                    <p><strong>Note:</strong> This process handles large datasets by processing records in chunks of 1000. Processing time depends on the amount of data.</p>

                    <button id="generate-fba-inventory-report" class="btn btn-primary">
                        Generate FBA Inventory Report
                    </button>
                    <div id="fba-inventory-report-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Generate Historical FBA Inventory Reports</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            Generates FBA inventory value reports for ALL months that have data, not just the latest month.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #6f42c1; margin-bottom: 15px;">
                        <p><strong>What this process does:</strong></p>
                        <ul>
                            <li>Finds ALL distinct months from Amazon Inventory Ledger Summary data</li>
                            <li>Generates a report for each month that has data</li>
                            <li>Updates existing reports or creates new ones as needed</li>
                            <li>Skips months that are locked (locked_at is not null)</li>
                            <li>Processes each month's data in chunks of 1000 records</li>
                            <li>Stores results in <code>rpt_amaz_monthly_fba_inventory_value</code> table</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Important Notes:</strong></p>
                        <ul>
                            <li><strong>Locked Reports:</strong> Months with locked_at set will be skipped entirely</li>
                            <li><strong>Large Dataset:</strong> This processes ALL historical data, which may take several minutes</li>
                            <li><strong>Overwrites Data:</strong> Unlocked existing reports will be updated with current calculations</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #e7f3ff; padding: 10px; border-left: 4px solid #0066cc; margin-bottom: 15px;">
                        <p><strong>Prerequisites:</strong></p>
                        <ol>
                            <li>Amazon Inventory Ledger Summary data must be imported for multiple months</li>
                            <li>FBA carrying costs materialized view should be refreshed</li>
                            <li>SDAINs must have <code>fba_sku</code> values that match Amazon MSKUs</li>
                        </ol>
                    </div>

                    <p><strong>When to run:</strong></p>
                    <ul>
                        <li>When you need to backfill historical FBA inventory valuations</li>
                        <li>After importing multiple months of Amazon data</li>
                        <li>When carrying costs have been updated and you want to recalculate all unlocked months</li>
                        <li>For comprehensive historical reporting and analysis</li>
                    </ul>

                    <p><strong>Note:</strong> This process can take several minutes depending on the amount of historical data. Progress will be logged during processing.</p>

                    <button id="generate-historical-fba-reports" class="btn btn-warning">
                        Generate Historical FBA Reports
                    </button>
                    <div id="historical-fba-reports-result" class="mt-3"></div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h2>Manage FBA Report Locks</h2>
                    <div class="tooltip">ⓘ
                        <span class="tooltiptext">
                            View, lock, and unlock monthly FBA inventory value reports to control which data can be modified.
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="form-group" style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545; margin-bottom: 15px;">
                        <p><strong>What this interface does:</strong></p>
                        <ul>
                            <li>Shows all monthly FBA inventory value reports</li>
                            <li>Displays lock status and variance between original and new values</li>
                            <li>Allows you to lock reports to prevent future modifications</li>
                            <li>Allows you to unlock reports (with confirmation) to allow modifications</li>
                            <li>Shows when reports were locked and by whom (if available)</li>
                        </ul>
                    </div>

                    <div class="form-group" style="background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin-bottom: 15px;">
                        <p><strong>Lock Management:</strong></p>
                        <ul>
                            <li><strong>Lock:</strong> Prevents the main columns from being updated during report generation</li>
                            <li><strong>Unlock:</strong> Allows the main columns to be updated (requires confirmation)</li>
                            <li><strong>Variance Detection:</strong> Shows if _new columns differ from main columns</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <button id="refresh-fba-locks" class="btn btn-info">
                            Refresh Report List
                        </button>
                    </div>

                    <div id="fba-locks-table-container">
                        <div class="alert alert-info">Click "Refresh Report List" to load the FBA reports.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Fetch README content
        fetch('README.md')
            .then(response => response.text())
            .then(text => {
                // Simple markdown to HTML conversion (very basic)
                let html = text
                    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/```sql([\s\S]*?)```/g, '<pre><code class="language-sql">$1</code></pre>')
                    .replace(/```bash([\s\S]*?)```/g, '<pre><code class="language-bash">$1</code></pre>')
                    .replace(/```json([\s\S]*?)```/g, '<pre><code class="language-json">$1</code></pre>')
                    .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                    .replace(/^\d\. (.*$)/gm, '<ol><li>$1</li></ol>')
                    .replace(/<\/ol><ol>/g, '');

                document.getElementById('readmeContent').innerHTML = html;
            })
            .catch(error => {
                document.getElementById('readmeContent').innerHTML = '<p>Error loading README: ' + error.message + '</p>';
            });

        // Enqueue task form submission
        document.getElementById('enqueueForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const imageId = document.getElementById('imageId').value;
            const delayMinutes = document.getElementById('delayMinutes').value || 0;

            const outputDiv = document.getElementById('enqueueOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Enqueueing task for image ID ' + imageId + ' with delay ' + delayMinutes + ' minutes...\n';

            // Call API to enqueue task
            fetch('/api/enqueue-task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ imageId, delayMinutes }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += 'Task enqueued successfully!\n';
                    if (data.details && data.details.output) {
                        outputDiv.innerHTML += data.details.output + '\n';
                    }
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });

            // Refresh task counts after a short delay
            setTimeout(refreshStatus, 2000);
        });



        // View tasks button
        document.getElementById('viewTasksBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('tasksOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Loading tasks...\n';

            // Call API to get tasks
            fetch('/api/tasks')
                .then(response => response.json())
                .then(data => {
                    outputDiv.innerHTML = '';

                    if (data.pendingTasks && data.pendingTasks.length > 0) {
                        outputDiv.innerHTML += 'Pending Tasks Ready to Process:\n\n';
                        outputDiv.innerHTML += JSON.stringify(data.pendingTasks, null, 2);
                        outputDiv.innerHTML += '\n\n';
                    } else {
                        outputDiv.innerHTML += 'No pending tasks ready to process.\n\n';
                    }

                    if (data.futureTasks && data.futureTasks.length > 0) {
                        outputDiv.innerHTML += 'Future Scheduled Tasks:\n\n';
                        outputDiv.innerHTML += JSON.stringify(data.futureTasks, null, 2);
                        outputDiv.innerHTML += '\n\n';
                    } else {
                        outputDiv.innerHTML += 'No future scheduled tasks.\n\n';
                    }

                    if (data.completedTasks && data.completedTasks.length > 0) {
                        outputDiv.innerHTML += 'Recently Completed Tasks:\n\n';

                        // Format completed tasks to highlight success messages
                        data.completedTasks.forEach(task => {
                            outputDiv.innerHTML += `Task ID: ${task.id}\n`;
                            outputDiv.innerHTML += `Processed At: ${task.processed_at}\n`;

                            if (task.result && task.result.message) {
                                const messageClass = task.result.message.includes('Success!') ? 'success-message' : 'failure-message';
                                outputDiv.innerHTML += `Result: <span class="${messageClass}">${task.result.message}</span>\n`;
                            } else if (task.result) {
                                outputDiv.innerHTML += `Result: ${JSON.stringify(task.result)}\n`;
                            }

                            outputDiv.innerHTML += '\n';
                        });
                    } else {
                        outputDiv.innerHTML += 'No recently completed tasks.\n';
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML = 'Error loading tasks: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
        });

        // Run worker button
        document.getElementById('runWorkerBtn').addEventListener('click', function() {
            const mode = document.getElementById('workerMode').value;
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = ''; // Clear previous output

            if (mode === 'once') {
                outputDiv.innerHTML = 'Running worker once...\n';

                // Call API to run worker once
                fetch('/api/worker/run-once', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML += 'Worker started successfully.\n';
                        outputDiv.innerHTML += 'The worker will process all available tasks and then exit.\n';
                    } else {
                        outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    }

                    // Refresh status after a short delay
                    setTimeout(refreshStatus, 1000);
                })
                .catch(error => {
                    outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
            } else {
                outputDiv.innerHTML = 'Starting worker daemon...\n';
                outputDiv.innerHTML += 'The worker will run continuously, checking for new tasks every 15 seconds and processing up to 100 tasks per run.\n';

                // Call API to start worker daemon
                fetch('/api/worker/start-daemon', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        outputDiv.innerHTML += 'Worker daemon started successfully!\n';
                        outputDiv.innerHTML += 'The worker will continue running until stopped.\n';

                        // Update status indicators and buttons
                        updateWorkerStatusIndicators('running');

                        // Start polling for status updates
                        startStatusPolling();
                    } else {
                        outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                    }
                })
                .catch(error => {
                    outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                    outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
                });
            }
        });

        // Stop worker button
        document.getElementById('stopWorkerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML += '\nStopping worker daemon...\n';

            // Call API to stop worker daemon
            fetch('/api/worker/stop-daemon', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update status indicators and buttons
                    updateWorkerStatusIndicators('stopped');

                    outputDiv.innerHTML += 'Worker daemon stopped successfully.\n';
                    outputDiv.innerHTML += 'All pending tasks will remain in the queue until the worker is started again.\n';

                    // Stop polling for status updates
                    stopStatusPolling();
                } else {
                    outputDiv.innerHTML += 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Refresh status button
        document.getElementById('refreshStatus').addEventListener('click', refreshStatus);

        // Function to refresh status
        function refreshStatus() {
            // First, fetch worker status
            fetch('/api/worker/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('pendingTasksCount').textContent = data.pendingTasksCount || 0;
                    document.getElementById('futureTasksCount').textContent = data.futureTasksCount || 0;

                    if (data.lastRunTime) {
                        const formattedTime = new Date(data.lastRunTime).toLocaleString();
                        document.getElementById('lastRunTime').textContent = formattedTime;
                        document.getElementById('workerTabLastRunTime').textContent = formattedTime;
                    } else {
                        document.getElementById('lastRunTime').textContent = 'Never';
                        document.getElementById('workerTabLastRunTime').textContent = 'Never';
                    }

                    // Update worker status indicators (both in dashboard and worker tab)
                    updateWorkerStatusIndicators(data.status);

                    // Update worker output if available
                    if (data.output && data.output.length > 0) {
                        // Show the output div in the worker tab
                        const outputDiv = document.getElementById('workerOutput');
                        outputDiv.style.display = 'block';

                        // Update both console logs
                        updateConsoleLog(data.output);
                    }
                })
                .catch(error => {
                    console.error('Error fetching worker status:', error);
                });

            // Then, directly fetch task type breakdown (don't wait for worker status)
            fetch('/api/tasks/by-type')
                .then(response => response.json())
                .then(data => {
                    if (!data.taskTypes) {
                        const tableBody = document.getElementById('taskTableBody');
                        tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">No task data available.</td></tr>';
                        return;
                    }

                    // Update the workflow table
                    updateWorkflowTable(
                        data.taskTypes.pending || {},
                        data.taskTypes.future || {},
                        data.taskTypes.completed || {},
                        data.taskTypes.error || {}
                    );
                })
                .catch(error => {
                    console.error('Error fetching task type breakdown:', error);
                    const tableBody = document.getElementById('taskTableBody');
                    tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading task statistics. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        // Function to update worker status indicators
        function updateWorkerStatusIndicators(status) {
            const statusIndicators = document.querySelectorAll('.status-indicator');

            if (status === 'running') {
                // Update all status indicators
                statusIndicators.forEach(indicator => {
                    indicator.classList.remove('status-stopped');
                    indicator.classList.add('status-running');
                });

                // Update status text
                document.getElementById('workerStatus').textContent = 'Running';
                document.getElementById('workerTabStatus').textContent = 'Running';

                // Update buttons
                document.getElementById('runWorkerBtn').style.display = 'none';
                document.getElementById('stopWorkerBtn').style.display = 'inline-block';
                document.getElementById('dashboardRunWorkerOnce').style.display = 'none';
            } else {
                // Update all status indicators
                statusIndicators.forEach(indicator => {
                    indicator.classList.remove('status-running');
                    indicator.classList.add('status-stopped');
                });

                // Update status text
                document.getElementById('workerStatus').textContent = 'Stopped';
                document.getElementById('workerTabStatus').textContent = 'Stopped';

                // Update buttons
                document.getElementById('runWorkerBtn').style.display = 'inline-block';
                document.getElementById('stopWorkerBtn').style.display = 'none';
                document.getElementById('dashboardRunWorkerOnce').style.display = 'inline-block';
            }
        }

        // Function to update console log
        function updateConsoleLog(output) {
            if (!output || output.length === 0) return;

            const dashboardConsoleLog = document.getElementById('dashboardConsoleLog');
            const workerOutput = document.getElementById('workerOutput');
            const showTimestamps = document.getElementById('showTimestampsCheckbox').checked;
            const filterType = document.getElementById('logFilterSelect').value;
            const taskTypeFilter = document.getElementById('taskTypeFilter').value.toLowerCase();

            // Parse and format the log entries
            let formattedOutput = '';

            output.forEach(line => {
                // Skip empty lines
                if (!line.trim()) return;

                // Add timestamp
                const timestamp = new Date().toLocaleTimeString();

                // Parse the line to extract task information
                let formattedLine = '';
                let logClass = '';

                // Check if this is a task-related log
                const taskIdMatch = line.match(/\[taskQueueWorker\.js\] Processing task (\d+) of type ([\w_]+)/);
                const taskCompletedMatch = line.match(/\[taskQueueWorker\.js\] Successfully (completed|updated|enqueued|matched|generated|processed)/);
                const taskErrorMatch = line.match(/\[taskQueueWorker\.js\] (Error|Exception|Failed)/);
                const taskProcessingMatch = line.match(/\[taskQueueWorker\.js\] (Processing|Fetching|Calculating|Finding|Updating)/);

                // Apply filters
                if (filterType !== 'all') {
                    if (filterType === 'error' && !taskErrorMatch) return;
                    if (filterType === 'success' && !taskCompletedMatch) return;
                    if (filterType === 'processing' && !taskProcessingMatch && !taskIdMatch) return;
                }

                // Apply task type filter
                if (taskTypeFilter && taskIdMatch) {
                    const taskType = taskIdMatch[2].toLowerCase();
                    if (!taskType.includes(taskTypeFilter)) return;
                }

                // Format based on content
                if (taskIdMatch) {
                    const taskId = taskIdMatch[1];
                    const taskType = taskIdMatch[2];

                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="task-id">Task #${taskId}</span>`;
                    formattedLine += `<span class="task-type">${taskType}</span>`;
                    formattedLine += `<span>Processing started</span></div>`;
                    logClass = 'processing';
                } else if (taskCompletedMatch) {
                    formattedLine = `<div class="log-entry completed">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line.replace(/\[taskQueueWorker\.js\]/, '');
                    formattedLine += `</div>`;
                    logClass = 'success';
                } else if (taskErrorMatch) {
                    formattedLine = `<div class="log-entry error">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line.replace(/\[taskQueueWorker\.js\]/, '');
                    formattedLine += `</div>`;
                    logClass = 'error';
                } else if (taskProcessingMatch) {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="processing">${line.replace(/\[taskQueueWorker\.js\]/, '')}</span></div>`;
                    logClass = 'info';
                } else if (line.includes('[taskQueueWorker.js]')) {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += `<span class="info">${line.replace(/\[taskQueueWorker\.js\]/, '')}</span></div>`;
                    logClass = 'info';
                } else {
                    formattedLine = `<div class="log-entry">`;
                    if (showTimestamps) {
                        formattedLine += `<span class="timestamp">${timestamp}</span>`;
                    }
                    formattedLine += line;
                    formattedLine += `</div>`;
                }

                formattedOutput += formattedLine;
            });

            // Update both console logs
            dashboardConsoleLog.innerHTML = formattedOutput || '<div class="log-entry info">No log entries match the current filters</div>';

            // Auto-scroll if enabled
            if (document.getElementById('autoScrollCheckbox').checked) {
                dashboardConsoleLog.scrollTop = dashboardConsoleLog.scrollHeight;
                if (workerOutput.style.display !== 'none') {
                    workerOutput.scrollTop = workerOutput.scrollHeight;
                }
            }
        }

        // Function to update the workflow table
        function updateWorkflowTable(pendingTasks, futureTasks, completedTasks, errorTasks) {
            // Define the workflow order of task types
            const workflowOrder = [
                // Image verification workflow
                'clear_disc_verification',
                'verify_disc_image',
                'check_if_disc_is_ready',
                'check_if_disc_ready_to_publish',
                'publish_disc',
                'publish_product_disc',

                // Field generation tasks
                'generate_disc_title_pull_and_handle',
                'generate_mps_fields',

                // Matching and inventory tasks
                'set_disc_carry_cost',
                'match_disc_to_asins',
                'match_disc_to_osl',
                'mps_price_verified_try_upload_osls',
                'mps_price_verified_osl_uploaded_look_for_discs',
                'toggle_osl_ready_button',
                'plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload',
                'reconcile_clear_count_from_shopify_for_sold_disc',
                'update_osl_after_publish',

                // Legacy image tasks
                'verify_t_images_image',
                'insert_new_t_images_record',
                'delete_t_images_record'
            ];

            const tableBody = document.getElementById('taskTableBody');
            tableBody.innerHTML = '';

            // Create a row for each task type in workflow order
            workflowOrder.forEach(taskType => {
                const pendingCount = pendingTasks[taskType] || 0;
                const futureCount = futureTasks[taskType] || 0;
                const completedCount = completedTasks[taskType] || 0;
                const errorCount = errorTasks[taskType] || 0;

                const row = document.createElement('tr');

                // Task name cell
                const nameCell = document.createElement('td');
                nameCell.className = 'task-name';
                nameCell.textContent = taskType;
                row.appendChild(nameCell);

                // Pending count cell
                const pendingCell = document.createElement('td');
                pendingCell.className = 'count ' + (pendingCount > 0 ? 'count-nonzero' : 'count-zero');
                pendingCell.textContent = pendingCount;
                row.appendChild(pendingCell);

                // Future count cell
                const futureCell = document.createElement('td');
                futureCell.className = 'count ' + (futureCount > 0 ? 'count-nonzero' : 'count-zero');
                futureCell.textContent = futureCount;
                row.appendChild(futureCell);

                // Completed count cell
                const completedCell = document.createElement('td');
                completedCell.className = 'count ' + (completedCount > 0 ? 'count-success' : 'count-zero');
                completedCell.textContent = completedCount;
                row.appendChild(completedCell);

                // Error count cell
                const errorCell = document.createElement('td');
                errorCell.className = 'count ' + (errorCount > 0 ? 'count-error' : 'count-zero');
                errorCell.textContent = errorCount;
                row.appendChild(errorCell);

                tableBody.appendChild(row);
            });

            // Show the table
            document.getElementById('taskWorkflowTable').style.display = 'block';
        }

        // Status polling
        let statusPollInterval = null;

        function startStatusPolling() {
            if (!statusPollInterval) {
                statusPollInterval = setInterval(refreshStatus, 5000);
            }
        }

        function stopStatusPolling() {
            if (statusPollInterval) {
                clearInterval(statusPollInterval);
                statusPollInterval = null;
            }
        }

        // Dashboard Run Worker Once button
        document.getElementById('dashboardRunWorkerOnce').addEventListener('click', function() {
            const outputDiv = document.getElementById('workerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running worker once from dashboard...';

            // Call API to run worker once
            fetch('/api/worker/run-once', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML += '\nWorker started successfully. Check the Worker tab for output.\n';

                    // Update last run time
                    document.getElementById('lastRunTime').textContent = new Date().toLocaleString();

                    // Refresh status after a short delay to allow worker to process tasks
                    setTimeout(refreshStatus, 2000);
                } else {
                    outputDiv.innerHTML += '\nError: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML += '\nError: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Initial status refresh
            refreshStatus();

            // Start polling for status updates
            startStatusPolling();
        });

        // Clear console button
        document.getElementById('clearConsoleBtn').addEventListener('click', function() {
            document.getElementById('dashboardConsoleLog').innerHTML = '';
        });

        // Log filter controls
        document.getElementById('logFilterSelect').addEventListener('change', function() {
            // Re-apply the current output with the new filter
            updateConsoleLog(workerOutput);
        });

        document.getElementById('taskTypeFilter').addEventListener('input', function() {
            // Re-apply the current output with the new filter
            updateConsoleLog(workerOutput);
        });

        document.getElementById('showTimestampsCheckbox').addEventListener('change', function() {
            // Re-apply the current output with the new timestamp setting
            updateConsoleLog(workerOutput);
        });

        // Import Veeqo Sellables button
        document.getElementById('importVeeqoSellablesBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importVeeqoSellablesOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Truncating imported_table_veeqo_sellables_export table and running import_veeqo_sellables.js...';

            // Call API to import Veeqo sellables
            fetch('/api/import-veeqo-sellables', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Veeqo sellables imported successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Delete Wrong Veeqo Records button
        document.getElementById('deleteWrongVeeqoRecordsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('deleteWrongVeeqoRecordsOutput');
            outputDiv.style.display = 'block';

            // Get the limit value
            const limitSelect = document.getElementById('deleteVeeqoLimit');
            const limit = limitSelect.value;

            // Update the message based on the limit
            let message = 'Identifying and deleting';
            if (limit === '1') {
                message += ' 1 Veeqo record';
            } else if (limit === '10') {
                message += ' 10 Veeqo records';
            } else {
                message += ' all Veeqo records';
            }
            message += ' with variant titles containing " (D#"...';

            outputDiv.innerHTML = message;

            // Confirm before proceeding
            let confirmMessage = 'This will permanently delete ';
            if (limit === '1') {
                confirmMessage += '1 Veeqo product variant';
            } else if (limit === '10') {
                confirmMessage += '10 Veeqo product variants';
            } else {
                confirmMessage += 'ALL Veeqo product variants';
            }
            confirmMessage += ' with variant titles containing " (D#". This action cannot be undone. Are you sure you want to proceed?';

            if (!confirm(confirmMessage)) {
                outputDiv.innerHTML = 'Operation cancelled by user.';
                return;
            }

            // Call API to delete wrong Veeqo records
            fetch('/api/delete-wrong-veeqo-records', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ limit: limit ? parseInt(limit) : null })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Wrong Veeqo records deleted successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nDeletion Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Successful deletions: ${data.summary.successCount}\n`;
                        outputDiv.innerHTML += `- Failed deletions: ${data.summary.failureCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile Discs to Veeqo button
        document.getElementById('reconcileDToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileDToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running reconcileDToVeeqo.js to update discs with inventory discrepancies...';

            // Call API to run reconcileDToVeeqo.js
            fetch('/api/reconcile-d-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Disc to Veeqo reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Enqueue Sold Discs Shopify Tasks button
        document.getElementById('enqueueSoldDiscsShopifyBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('enqueueSoldDiscsShopifyOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Finding sold discs still showing on Shopify and enqueueing tasks...';

            // Call API to enqueue tasks
            fetch('/api/enqueue-sold-discs-shopify-tasks', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        data.tasks.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.id}, Disc ID: ${task.disc_id}\n`;
                        });
                        outputDiv.innerHTML += '\nYou can now process these tasks one at a time from the task queue.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile RPRO to Veeqo button
        document.getElementById('reconcileRproToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileRproToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Refreshing RPRO to Veeqo reconciliation data...';

            // Call API to refresh reconciliation data
            fetch('/api/reconcile-rpro-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'RPRO to Veeqo reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.stats) {
                        outputDiv.innerHTML += '\nReconciliation Statistics:\n';
                        outputDiv.innerHTML += `- Total records: ${data.stats.totalCount}\n`;
                        outputDiv.innerHTML += `- Records with discrepancies: ${data.stats.discrepancyCount}\n`;
                        outputDiv.innerHTML += `- Records where RPRO has more: ${data.stats.rproMoreCount}\n`;
                        outputDiv.innerHTML += `- Records where Veeqo has more: ${data.stats.veeqoMoreCount}\n`;
                        outputDiv.innerHTML += `- Records with matching quantities: ${data.stats.matchingCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Update Veeqo from RPRO button
        document.getElementById('updateVeeqoFromRproBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('updateVeeqoFromRproOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating Veeqo quantities from RPRO data...';

            // Confirm before proceeding
            if (!confirm('This will update Veeqo quantities to match RPRO quantities for all records with discrepancies. Are you sure you want to proceed?')) {
                outputDiv.innerHTML = 'Operation cancelled by user.';
                return;
            }

            // Call API to update Veeqo quantities
            fetch('/api/update-veeqo-from-rpro', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Veeqo quantities updated successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nUpdate Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Successful updates: ${data.summary.successCount}\n`;
                        outputDiv.innerHTML += `- Failed updates: ${data.summary.failureCount}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile OSL Stats button
        document.getElementById('reconcileOslStatsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileOslStatsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating t_inv_osl records with correct quantity counts from v_stats_by_osl...';

            // Call API to reconcile OSL stats
            fetch('/api/reconcile-osl-stats', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'OSL stats reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total batches: ${data.summary.totalBatches}\n`;
                        outputDiv.innerHTML += `- Total records updated: ${data.summary.totalUpdated}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile SDAsin Stats button
        document.getElementById('reconcileSdasinStatsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileSdasinStatsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Updating t_inv_sdasin records with correct quantity counts from v_stats_by_sdasin...';

            // Call API to reconcile SDAsin stats
            fetch('/api/reconcile-sdasin-stats', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'SDAsin stats reconciliation completed successfully!\n';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += data.details.stdout + '\n';
                    }

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total batches: ${data.summary.totalBatches}\n`;
                        outputDiv.innerHTML += `- Total records updated: ${data.summary.totalUpdated}\n`;
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                    if (data.stdout) outputDiv.innerHTML += 'Output: ' + data.stdout + '\n';
                    if (data.stderr) outputDiv.innerHTML += 'Error output: ' + data.stderr + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import Shopify Matrixify Export button
        document.getElementById('importShopifyMatrixifyBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importShopifyMatrixifyOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Importing Shopify Matrixify export data to imported_table_shopify_products_dz...';

            // Call API to import Shopify Matrixify export
            fetch('/api/import-shopify-matrixify', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Shopify Matrixify export imported successfully to imported_table_shopify_products_dz.<br>';
                    if (data.details && data.details.stdout) {
                        outputDiv.innerHTML += '<pre>' + data.details.stdout + '</pre>';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + data.error + '<br>';
                    if (data.stdout) {
                        outputDiv.innerHTML += '<pre>' + data.stdout + '</pre>';
                    }
                    if (data.stderr) {
                        outputDiv.innerHTML += '<pre class="error">' + data.stderr + '</pre>';
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Reconcile OSLs to Veeqo button
        document.getElementById('reconcileOSLToVeeqoBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('reconcileOSLToVeeqoOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Finding OSLs with inventory discrepancies in Veeqo and enqueueing tasks...';

            // Call API to enqueue tasks
            fetch('/api/reconcile-osl-to-veeqo', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.summary) {
                        outputDiv.innerHTML += '\nReconciliation Summary:\n';
                        outputDiv.innerHTML += `- Total records processed: ${data.summary.totalProcessed}\n`;
                        outputDiv.innerHTML += `- Records skipped (missing available_quantity): ${data.summary.skippedRecords}\n`;
                        outputDiv.innerHTML += `- Tasks enqueued: ${data.summary.tasksEnqueued}\n`;
                    }

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        // Limit to showing first 20 tasks to avoid overwhelming the UI
                        const tasksToShow = data.tasks.slice(0, 20);
                        tasksToShow.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.id}, OSL ID: ${task.osl_id}\n`;
                        });

                        if (data.tasks.length > 20) {
                            outputDiv.innerHTML += `... and ${data.tasks.length - 20} more tasks\n`;
                        }

                        outputDiv.innerHTML += '\nYou can now process these tasks one at a time from the task queue.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // Import Discs from Google Sheets form
        document.getElementById('importDiscsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const googleSheetsUrl = document.getElementById('googleSheetsUrl').value;
            const validateOnly = document.getElementById('validateOnly').checked;
            const outputDiv = document.getElementById('importDiscsOutput');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = validateOnly ? 'Validating disc data from Google Sheets...' : 'Importing disc data from Google Sheets...';

            // Call API to import discs
            fetch('/api/import-discs-from-sheets', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    googleSheetsUrl: googleSheetsUrl,
                    validateOnly: validateOnly
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '<br>';

                    if (data.validationResults) {
                        outputDiv.innerHTML += '<h4>Validation Results:</h4>';
                        outputDiv.innerHTML += `<p>Total records: ${data.validationResults.totalRecords}</p>`;
                        outputDiv.innerHTML += `<p>Valid records: ${data.validationResults.validRecords}</p>`;
                        outputDiv.innerHTML += `<p>Invalid records: ${data.validationResults.invalidRecords}</p>`;

                        if (data.validationResults.errors && data.validationResults.errors.length > 0) {
                            outputDiv.innerHTML += '<h5>Validation Errors:</h5><ul>';
                            data.validationResults.errors.forEach(error => {
                                outputDiv.innerHTML += `<li>Row ${error.row}: ${error.message}</li>`;
                            });
                            outputDiv.innerHTML += '</ul>';
                        }
                    }

                    if (data.importResults && !validateOnly) {
                        outputDiv.innerHTML += '<h4>Import Results:</h4>';
                        outputDiv.innerHTML += `<p>Records imported: ${data.importResults.recordsImported}</p>`;

                        if (data.importResults.newIds && data.importResults.newIds.length > 0) {
                            outputDiv.innerHTML += '<h5>New IDs (copy these back to your Google Sheet):</h5>';
                            outputDiv.innerHTML += '<textarea rows="10" cols="50" readonly>';
                            data.importResults.newIds.forEach((id, index) => {
                                outputDiv.innerHTML += `Row ${index + 2}: ${id}\n`; // +2 because row 1 is header
                            });
                            outputDiv.innerHTML += '</textarea>';
                        }
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '<br>';
                    if (data.details) {
                        outputDiv.innerHTML += 'Details: ' + data.details + '<br>';
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '<br>';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Import Discs from Google Sheets form
        document.getElementById('importDiscsForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const googleSheetsUrl = document.getElementById('googleSheetsUrl').value;
            const validateOnly = document.getElementById('validateOnly').checked;
            const outputDiv = document.getElementById('importDiscsOutput');

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = validateOnly ? 'Validating disc data from Google Sheets...' : 'Importing disc data from Google Sheets...';

            // Call API to import discs
            fetch('/api/import-discs-from-sheets', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    googleSheetsUrl: googleSheetsUrl,
                    validateOnly: validateOnly
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '<br>';

                    if (data.validationResults) {
                        outputDiv.innerHTML += '<h4>Validation Results:</h4>';
                        outputDiv.innerHTML += `<p>Total records: ${data.validationResults.totalRecords}</p>`;
                        outputDiv.innerHTML += `<p>Valid records: ${data.validationResults.validRecords}</p>`;
                        outputDiv.innerHTML += `<p>Invalid records: ${data.validationResults.invalidRecords}</p>`;
                        outputDiv.innerHTML += `<p>Empty records (skipped): ${data.validationResults.emptyRecords || 0}</p>`;

                        if (data.validationResults.errors && data.validationResults.errors.length > 0) {
                            outputDiv.innerHTML += '<h5>Validation Errors:</h5><ul>';
                            data.validationResults.errors.forEach(error => {
                                outputDiv.innerHTML += `<li>Row ${error.row}: ${error.message}</li>`;
                            });
                            outputDiv.innerHTML += '</ul>';
                        }
                    }

                    if (data.importResults && !validateOnly) {
                        outputDiv.innerHTML += '<h4>Import Results:</h4>';
                        outputDiv.innerHTML += `<p>Records imported: ${data.importResults.recordsImported}</p>`;

                        if (data.importResults.newIds && data.importResults.newIds.length > 0) {
                            outputDiv.innerHTML += '<h5>New IDs (copy these back to your Google Sheet):</h5>';
                            outputDiv.innerHTML += '<textarea rows="10" cols="50" readonly>';
                            data.importResults.newIds.forEach((id, index) => {
                                outputDiv.innerHTML += `Row ${index + 2}: ${id}\n`; // +2 because row 1 is header
                            });
                            outputDiv.innerHTML += '</textarea>';
                        }
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '<br>';
                    if (data.details) {
                        outputDiv.innerHTML += 'Details: ' + data.details + '<br>';
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '<br>';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Generate OSL Fields for Null G_Code button
        document.getElementById('enqueueGenerateOslFieldsBtn').addEventListener('click', function() {
            const batchSize = document.getElementById('oslBatchSize').value || 100;
            const outputDiv = document.getElementById('enqueueGenerateOslFieldsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Finding OSLs with null g_code and enqueueing tasks (batch size: ${batchSize})...`;

            // Call API to enqueue tasks
            fetch('/api/enqueue-generate-osl-fields', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ batchSize }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = data.message + '\n';

                    if (data.count > 0) {
                        outputDiv.innerHTML += '\nEnqueued tasks:\n';
                        // Limit to showing first 20 tasks to avoid overwhelming the UI
                        const tasksToShow = data.tasks.slice(0, 20);
                        tasksToShow.forEach(task => {
                            outputDiv.innerHTML += `- Task ID: ${task.task_id}, OSL ID: ${task.osl_id}\n`;
                        });

                        if (data.tasks.length > 20) {
                            outputDiv.innerHTML += `... and ${data.tasks.length - 20} more tasks\n`;
                        }

                        outputDiv.innerHTML += '\nYou can now process these tasks using the task queue worker.\n';
                    } else {
                        outputDiv.innerHTML += '\nNo OSLs with null g_code were found.\n';
                    }
                } else {
                    outputDiv.innerHTML = 'Error: ' + (data.error || 'Unknown error') + '\n';
                }
            })
            .catch(error => {
                outputDiv.innerHTML = 'Error: ' + error.message + '\n';
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.\n';
            });
        });

        // No need for this anymore as we call refreshStatus() on DOMContentLoaded

        // Informed tab functionality

        // Function to refresh Informed report status
        function refreshInformedStatus() {
            const statusDiv = document.getElementById('informedReportStatus');

            // Call API to get report status
            fetch('/api/informed/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let statusHtml = '<table class="task-table" style="width: 100%;">';
                        statusHtml += '<thead><tr><th>Report</th><th>Last Updated</th><th>Record Count</th><th>Status</th></tr></thead>';
                        statusHtml += '<tbody>';

                        // Add row for each report
                        data.reports.forEach(report => {
                            const statusClass = report.status === 'OK' ? 'success-message' : 'failure-message';
                            const formattedDate = report.lastUpdated ? new Date(report.lastUpdated).toLocaleString() : 'Never';

                            statusHtml += `<tr>
                                <td>${report.name}</td>
                                <td>${formattedDate}</td>
                                <td>${report.recordCount || 0}</td>
                                <td class="${statusClass}">${report.status}</td>
                            </tr>`;
                        });

                        statusHtml += '</tbody></table>';
                        statusDiv.innerHTML = statusHtml;
                    } else {
                        statusDiv.innerHTML = `<p class="failure-message">Error: ${data.error || 'Failed to get report status'}</p>`;
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = `<p class="failure-message">Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>`;
                });

            // Call API to get scheduler status
            const schedulerStatusDiv = document.getElementById('informedSchedulerStatus');

            fetch('/api/informed/scheduler-status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let statusHtml = '<p>';

                        if (data.enabled) {
                            statusHtml += '<span class="success-message">✓ Scheduler is enabled</span><br>';
                            statusHtml += `Next run: ${new Date(data.nextRun).toLocaleString()}<br>`;
                            statusHtml += `Schedule: ${data.schedule}`;
                        } else {
                            statusHtml += '<span class="failure-message">✗ Scheduler is disabled</span>';
                        }

                        statusHtml += '</p>';
                        schedulerStatusDiv.innerHTML = statusHtml;

                        // Update button visibility
                        document.getElementById('enableInformedSchedulerBtn').style.display = data.enabled ? 'none' : 'inline-block';
                        document.getElementById('disableInformedSchedulerBtn').style.display = data.enabled ? 'inline-block' : 'none';
                    } else {
                        schedulerStatusDiv.innerHTML = `<p class="failure-message">Error: ${data.error || 'Failed to get scheduler status'}</p>`;
                    }
                })
                .catch(error => {
                    schedulerStatusDiv.innerHTML = `<p class="failure-message">Error: ${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>`;
                });
        }

        // Download Informed Reports button
        document.getElementById('downloadInformedReportsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('downloadInformedReportsOutput');
            const maxRetries = parseInt(document.getElementById('maxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('retryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Downloading reports from Informed Repricer (max retries: ${maxRetries}, retry interval: ${retryInterval}s)...<br>`;
            outputDiv.innerHTML += 'This may take several minutes as we wait for reports to be generated...<br>';
            outputDiv.innerHTML += '<div id="downloadProgress">Requesting reports...</div>';

            // Create a progress indicator
            const progressDiv = document.getElementById('downloadProgress');
            let dots = 0;
            const progressInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                const dotsStr = '.'.repeat(dots);
                progressDiv.innerHTML = `Waiting for reports to be generated${dotsStr}`;
            }, 500);

            // Disable the button during download
            const downloadBtn = document.getElementById('downloadInformedReportsBtn');
            downloadBtn.disabled = true;
            downloadBtn.innerHTML = 'Downloading...';

            // Call API to download reports
            fetch('/api/informed/download-reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = 'Download Reports from Informed';

                if (data.success) {
                    outputDiv.innerHTML = 'Reports downloaded successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<ul>';
                        data.reports.forEach(report => {
                            if (report.success) {
                                outputDiv.innerHTML += `<li>${report.report}: Success</li>`;
                            } else {
                                outputDiv.innerHTML += `<li>${report.report}: Failed - ${report.error}</li>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after download
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to download reports'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                downloadBtn.disabled = false;
                downloadBtn.innerHTML = 'Download Reports from Informed';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Import Informed Reports button
        document.getElementById('importInformedReportsBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('importInformedReportsOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Importing reports to Supabase...';

            // Call API to import reports
            fetch('/api/informed/import-reports', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Reports imported successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.name}: ${report.recordCount} records imported</li>`;
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after import
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to import reports'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Full Informed Process button
        document.getElementById('runFullInformedProcessBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runFullInformedProcessOutput');
            const maxRetries = parseInt(document.getElementById('fullProcessMaxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('fullProcessRetryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = `Running full Informed process (max retries: ${maxRetries}, retry interval: ${retryInterval}s)...<br>`;
            outputDiv.innerHTML += 'This may take several minutes as we wait for reports to be generated...<br>';
            outputDiv.innerHTML += '<div id="fullProcessProgress">Requesting reports...</div>';

            // Create a progress indicator
            const progressDiv = document.getElementById('fullProcessProgress');
            let dots = 0;
            const progressInterval = setInterval(() => {
                dots = (dots + 1) % 4;
                const dotsStr = '.'.repeat(dots);
                progressDiv.innerHTML = `Waiting for reports to be generated${dotsStr}`;
            }, 500);

            // Disable the button during processing
            const processBtn = document.getElementById('runFullInformedProcessBtn');
            processBtn.disabled = true;
            processBtn.innerHTML = 'Running...';

            // Call API to run full process
            fetch('/api/informed/run-full-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                processBtn.disabled = false;
                processBtn.innerHTML = 'Run Full Process (Download & Import)';

                if (data.success) {
                    outputDiv.innerHTML = 'Full process completed successfully!<br>';

                    if (data.reports) {
                        outputDiv.innerHTML += '<h4>Download Results:</h4><ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.report}: ${report.downloadStatus}</li>`;
                            if (report.downloadError) {
                                outputDiv.innerHTML += `<ul><li class="error">Error: ${report.downloadError}</li></ul>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';

                        outputDiv.innerHTML += '<h4>Import Results:</h4><ul>';
                        data.reports.forEach(report => {
                            outputDiv.innerHTML += `<li>${report.report}: ${report.importStatus} (${report.recordCount} records)</li>`;
                            if (report.importError) {
                                outputDiv.innerHTML += `<ul><li class="error">Error: ${report.importError}</li></ul>`;
                            }
                        });
                        outputDiv.innerHTML += '</ul>';
                    }

                    // Refresh status after full process
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run full process'}<br>`;
                    if (data.details) {
                        outputDiv.innerHTML += `Details: ${data.details}<br>`;
                    }
                }
            })
            .catch(error => {
                // Clear the progress indicator
                clearInterval(progressInterval);

                // Re-enable the button
                processBtn.disabled = false;
                processBtn.innerHTML = 'Run Full Process (Download & Import)';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Complete Workflow button (Download + Import + Upload)
        document.getElementById('runCompleteWorkflowBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runCompleteWorkflowOutput');

            // Get retry parameters from the same inputs as individual download button
            const maxRetries = parseInt(document.getElementById('maxRetries').value) || 30;
            const retryInterval = parseInt(document.getElementById('retryInterval').value) || 10;

            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running complete end-to-end workflow...<br>';
            outputDiv.innerHTML += 'This will download reports, import them, then upload pricing data to Informed.<br>';
            outputDiv.innerHTML += `Using retry parameters: max retries=${maxRetries}, retry interval=${retryInterval}s<br><br>`;

            // Disable the button during processing
            const workflowBtn = document.getElementById('runCompleteWorkflowBtn');
            workflowBtn.disabled = true;
            workflowBtn.innerHTML = 'Running Complete Workflow...';

            // Call API to run complete workflow (same as individual buttons)
            fetch('/api/informed/run-complete-workflow', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    maxRetries: maxRetries,
                    retryInterval: retryInterval * 1000 // Convert to milliseconds
                })
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = '⚡ Run Complete Workflow';

                if (data.success) {
                    outputDiv.innerHTML = 'Complete end-to-end workflow completed successfully!<br><br>';

                    if (data.results) {
                        // Show results for each phase
                        if (data.results.download) {
                            outputDiv.innerHTML += `<strong>Phase 1 - Download Reports:</strong> ${data.results.download.success ? 'Success' : 'Failed'}<br>`;
                        }

                        if (data.results.import) {
                            outputDiv.innerHTML += `<strong>Phase 2 - Import Reports:</strong> ${data.results.import.success ? 'Success' : 'Failed'}<br>`;
                        }

                        if (data.results.upload) {
                            outputDiv.innerHTML += `<strong>Phase 3 - Upload Pricing:</strong> ${data.results.upload.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.upload.results && data.results.upload.results.upload && data.results.upload.results.upload.result) {
                                outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.results.upload.results.upload.result)}<br>`;
                            }
                        }
                    }

                    // Refresh status after complete workflow
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run complete workflow'}<br>`;

                    if (data.results) {
                        outputDiv.innerHTML += '<br><strong>Phase Results:</strong><br>';
                        Object.keys(data.results).forEach(phase => {
                            if (data.results[phase]) {
                                outputDiv.innerHTML += `${phase}: ${data.results[phase].success ? 'Success' : 'Failed'}`;
                                if (data.results[phase].error) {
                                    outputDiv.innerHTML += ` - ${data.results[phase].error}`;
                                }
                                outputDiv.innerHTML += '<br>';
                            }
                        });
                    }
                }
            })
            .catch(error => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = '⚡ Run Complete Workflow';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Enable Informed Scheduler button
        document.getElementById('enableInformedSchedulerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('informedSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Enabling Informed scheduler...';

            // Call API to enable scheduler
            fetch('/api/informed/enable-scheduler', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Scheduler enabled successfully!<br>';
                    outputDiv.innerHTML += `Next run: ${new Date(data.nextRun).toLocaleString()}<br>`;
                    outputDiv.innerHTML += `Schedule: ${data.schedule}`;

                    // Refresh status after enabling
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to enable scheduler'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Disable Informed Scheduler button
        document.getElementById('disableInformedSchedulerBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('informedSchedulerOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Disabling Informed scheduler...';

            // Call API to disable scheduler
            fetch('/api/informed/disable-scheduler', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = 'Scheduler disabled successfully!<br>';

                    // Refresh status after disabling
                    refreshInformedStatus();
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to disable scheduler'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Truncate tu_informed button
        document.getElementById('truncateTuInformedBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('truncateTuInformedOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Truncating tu_informed table...';

            // Call API to truncate tu_informed
            fetch('/api/informed/truncate-tu-informed', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `tu_informed truncated successfully!<br>`;
                    outputDiv.innerHTML += `Deleted ${data.deletedCount || 0} records.<br>`;
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to truncate tu_informed'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Fill tu_informed button
        document.getElementById('fillTuInformedBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('fillTuInformedOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Filling tu_informed from v_informed_upload...';

            // Call API to fill tu_informed
            fetch('/api/informed/fill-tu-informed', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `tu_informed filled successfully!<br>`;
                    outputDiv.innerHTML += `Inserted ${data.recordCount || 0} records.<br>`;
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to fill tu_informed'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Export CSV button
        document.getElementById('exportCsvBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('exportCsvOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Generating CSV export from tu_informed...';

            // Call API to export CSV
            fetch('/api/informed/export-csv', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `CSV export generated successfully!<br>`;
                    const lines = data.csvContent.split('\n').length - 1;
                    outputDiv.innerHTML += `Generated CSV with ${lines} data rows.<br>`;

                    // Store CSV content for upload step
                    window.lastGeneratedCsv = data.csvContent;

                    // Show preview of first few lines
                    const previewLines = data.csvContent.split('\n').slice(0, 5);
                    outputDiv.innerHTML += '<br><strong>Preview:</strong><br>';
                    outputDiv.innerHTML += '<pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 150px; overflow-y: auto;">';
                    outputDiv.innerHTML += previewLines.join('\n');
                    if (lines > 4) {
                        outputDiv.innerHTML += '\n... and ' + (lines - 4) + ' more rows';
                    }
                    outputDiv.innerHTML += '</pre>';
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to export CSV'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Upload CSV button
        document.getElementById('uploadCsvBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('uploadCsvOutput');
            outputDiv.style.display = 'block';

            // Check if we have CSV content from the export step
            if (!window.lastGeneratedCsv) {
                outputDiv.innerHTML = 'Error: No CSV content available. Please run "Export CSV" first.<br>';
                return;
            }

            outputDiv.innerHTML = 'Uploading CSV to Informed Repricer...';

            // Call API to upload CSV
            fetch('/api/informed/upload-csv', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    csvContent: window.lastGeneratedCsv
                })
            })
            .then(response => {
                if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error(`HTTP ${response.status}: ${text}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    outputDiv.innerHTML = `CSV uploaded to Informed successfully!<br>`;
                    if (data.result) {
                        outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.result)}<br>`;
                    }
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to upload CSV'}<br>`;
                }
            })
            .catch(error => {
                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Run Upload Workflow button
        document.getElementById('runUploadWorkflowBtn').addEventListener('click', function() {
            const outputDiv = document.getElementById('runUploadWorkflowOutput');
            outputDiv.style.display = 'block';
            outputDiv.innerHTML = 'Running complete upload workflow...<br>';
            outputDiv.innerHTML += 'This will truncate tu_informed, fill it with fresh data, generate CSV, and upload to Informed.<br><br>';

            // Disable the button during processing
            const workflowBtn = document.getElementById('runUploadWorkflowBtn');
            workflowBtn.disabled = true;
            workflowBtn.innerHTML = 'Running Workflow...';

            // Call API to run complete workflow
            fetch('/api/informed/run-upload-workflow', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = 'Run Complete Upload Workflow';

                if (data.success) {
                    outputDiv.innerHTML = 'Complete upload workflow completed successfully!<br><br>';

                    if (data.results) {
                        // Show results for each step
                        if (data.results.truncate) {
                            outputDiv.innerHTML += `<strong>Step 1 - Truncate:</strong> ${data.results.truncate.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.truncate.deletedCount !== undefined) {
                                outputDiv.innerHTML += `Deleted ${data.results.truncate.deletedCount} records.<br>`;
                            }
                        }

                        if (data.results.fill) {
                            outputDiv.innerHTML += `<strong>Step 2 - Fill:</strong> ${data.results.fill.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.fill.recordCount !== undefined) {
                                outputDiv.innerHTML += `Inserted ${data.results.fill.recordCount} records.<br>`;
                            }
                        }

                        if (data.results.export) {
                            outputDiv.innerHTML += `<strong>Step 3 - Export:</strong> ${data.results.export.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.export.csvContent) {
                                const lines = data.results.export.csvContent.split('\n').length - 1;
                                outputDiv.innerHTML += `Generated CSV with ${lines} data rows.<br>`;
                            }
                        }

                        if (data.results.upload) {
                            outputDiv.innerHTML += `<strong>Step 4 - Upload:</strong> ${data.results.upload.success ? 'Success' : 'Failed'}<br>`;
                            if (data.results.upload.result) {
                                outputDiv.innerHTML += `Upload result: ${JSON.stringify(data.results.upload.result)}<br>`;
                            }
                        }
                    }
                } else {
                    outputDiv.innerHTML = `Error: ${data.error || 'Failed to run upload workflow'}<br>`;

                    if (data.results) {
                        outputDiv.innerHTML += '<br><strong>Step Results:</strong><br>';
                        Object.keys(data.results).forEach(step => {
                            if (data.results[step]) {
                                outputDiv.innerHTML += `${step}: ${data.results[step].success ? 'Success' : 'Failed'}`;
                                if (data.results[step].error) {
                                    outputDiv.innerHTML += ` - ${data.results[step].error}`;
                                }
                                outputDiv.innerHTML += '<br>';
                            }
                        });
                    }
                }
            })
            .catch(error => {
                // Re-enable the button
                workflowBtn.disabled = false;
                workflowBtn.innerHTML = 'Run Complete Upload Workflow';

                outputDiv.innerHTML = `Error: ${error.message}<br>`;
                outputDiv.innerHTML += 'Make sure the adminServer.js is running.<br>';
            });
        });

        // Amazon FBA Import button
        document.getElementById('run-amazon-import').addEventListener('click', function() {
            const outputDiv = document.getElementById('amazon-import-result');
            const button = document.getElementById('run-amazon-import');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Running import...';
            outputDiv.innerHTML = '<div class="alert alert-info">Import in progress. This may take a few minutes...</div>';

            // Call API to import Amazon FBA data
            fetch('/api/import-amazon-fba', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Run Amazon FBA Import';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Import Successful!</h4>
                            <p>Imported ${data.importCount || 0} records into the database.</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Import Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Run Amazon FBA Import';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Import Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // FBA Carrying Costs Refresh button
        document.getElementById('refresh-fba-carrying-costs').addEventListener('click', function() {
            const outputDiv = document.getElementById('fba-carrying-costs-result');
            const button = document.getElementById('refresh-fba-carrying-costs');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Refreshing...';
            outputDiv.innerHTML = '<div class="alert alert-info">Refreshing materialized view. This may take a few minutes...</div>';

            // Call API to refresh FBA carrying costs
            fetch('/api/refresh-fba-carrying-costs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Refresh FBA Carrying Costs';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Refresh Successful!</h4>
                            <p>Materialized view refreshed successfully.</p>
                            <p><strong>Records processed:</strong> ${data.recordCount || 'Unknown'}</p>
                            <p><strong>Duration:</strong> ${data.duration || 'Unknown'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Refresh Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Refresh FBA Carrying Costs';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Refresh Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // FBA Inventory Report Generation button
        document.getElementById('generate-fba-inventory-report').addEventListener('click', function() {
            const outputDiv = document.getElementById('fba-inventory-report-result');
            const button = document.getElementById('generate-fba-inventory-report');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Generating report...';
            outputDiv.innerHTML = '<div class="alert alert-info">Generating FBA inventory value report. This may take several minutes for large datasets...</div>';

            // Call API to generate FBA inventory report
            fetch('/api/generate-fba-inventory-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Inventory Report';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Report Generated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Report Month:</strong> ${data.reportMonth || 'Unknown'}</p>
                                    <p><strong>Records Processed:</strong> ${data.processedRecords || 'Unknown'}</p>
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Total Units:</strong> ${data.totalUnits?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Total Value:</strong> $${data.totalValue?.toLocaleString() || 'Unknown'}</p>
                                    <p><strong>Avg Unit Cost:</strong> $${data.weightedAvgCost || 'Unknown'}</p>
                                </div>
                            </div>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Report Generation Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate FBA Inventory Report';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Report Generation Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // Historical FBA Reports Generation button
        document.getElementById('generate-historical-fba-reports').addEventListener('click', function() {
            const outputDiv = document.getElementById('historical-fba-reports-result');
            const button = document.getElementById('generate-historical-fba-reports');

            // Disable button and show loading
            button.disabled = true;
            button.innerHTML = 'Generating historical reports...';
            outputDiv.innerHTML = '<div class="alert alert-info">Generating historical FBA inventory value reports for all months. This may take several minutes for large datasets...</div>';

            // Call API to generate historical FBA reports
            fetch('/api/generate-historical-fba-reports', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate Historical FBA Reports';

                if (data.success) {
                    outputDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h4>Historical Reports Generated Successfully!</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Months Processed:</strong> ${data.monthsProcessed || 0}</p>
                                    <p><strong>Months Locked (updated _new columns):</strong> ${data.monthsLocked || 0}</p>
                                    <p><strong>Total Records Processed:</strong> ${data.totalRecordsProcessed?.toLocaleString() || 'Unknown'}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Processing Time:</strong> ${data.processingTime || 'Unknown'}</p>
                                    <p><strong>Status:</strong> Complete</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h5>Month-by-Month Details:</h5>
                                <pre style="max-height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 4px;">${data.details || 'No details available'}</pre>
                            </div>
                        </div>
                    `;
                } else {
                    outputDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Historical Reports Generation Failed</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                            <pre class="mt-3" style="max-height: 200px; overflow-y: auto;">${data.details || ''}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                // Re-enable button
                button.disabled = false;
                button.innerHTML = 'Generate Historical FBA Reports';

                outputDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Historical Reports Generation Failed</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        });

        // FBA Report Locks Management
        document.getElementById('refresh-fba-locks').addEventListener('click', function() {
            refreshFbaLocksList();
        });

        function refreshFbaLocksList() {
            const container = document.getElementById('fba-locks-table-container');
            container.innerHTML = '<div class="alert alert-info">Loading FBA reports...</div>';

            fetch('/api/get-fba-reports', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayFbaReportsTable(data.reports);
                } else {
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <h4>Failed to Load Reports</h4>
                            <p>${data.error || 'Unknown error occurred'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <h4>Failed to Load Reports</h4>
                        <p>${error.message}</p>
                        <p>Make sure the adminServer.js is running.</p>
                    </div>
                `;
            });
        }

        function displayFbaReportsTable(reports) {
            const container = document.getElementById('fba-locks-table-container');

            if (!reports || reports.length === 0) {
                container.innerHTML = '<div class="alert alert-warning">No FBA reports found.</div>';
                return;
            }

            let tableHtml = `
                <style>
                    .fba-table {
                        border-collapse: collapse;
                    }
                    .fba-table th, .fba-table td {
                        border: 1px solid #dee2e6 !important;
                        padding: 8px !important;
                        white-space: nowrap;
                    }
                    .fba-table th {
                        white-space: normal;
                        word-wrap: break-word;
                    }
                </style>
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-bordered fba-table">
                        <thead class="table-dark">
                            <tr>
                                <th style="text-align: right;">Month</th>
                                <th style="text-align: right;">Units</th>
                                <th style="text-align: right;">Avg<br>Cost</th>
                                <th style="text-align: right;">EOM<br>Value</th>
                                <th style="text-align: right;">Units<br>(New)</th>
                                <th style="text-align: right;">Avg Cost<br>(New)</th>
                                <th style="text-align: right;">EOM Value<br>(New)</th>
                                <th style="text-align: center;">🔒</th>
                                <th style="text-align: right;">Locked<br>At</th>
                                <th style="text-align: center;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            reports.forEach(report => {
                const monthDisplay = new Date(report.month).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long'
                });

                const isLocked = report.locked_at !== null;

                // Format main values
                const units = (report.total_ending_units || 0).toLocaleString();
                const avgCost = '$' + (report.avg_unit_cost || 0).toFixed(2);
                const eomValue = '$' + (report.sum_of_extended_values || 0).toLocaleString();

                // Format new values (show if they exist, otherwise show dash)
                const unitsNew = report.total_ending_units_new !== null ?
                    report.total_ending_units_new.toLocaleString() : '-';
                const avgCostNew = report.avg_unit_cost_new !== null ?
                    '$' + report.avg_unit_cost_new.toFixed(2) : '-';
                const eomValueNew = report.sum_of_extended_values_new !== null ?
                    '$' + report.sum_of_extended_values_new.toLocaleString() : '-';

                // Lock status icon only
                const lockIcon = isLocked ? '🔒' : '🔓';

                const lockedAtDisplay = isLocked ?
                    new Date(report.locked_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    }) :
                    '-';

                const actionButton = isLocked ?
                    `<button class="btn btn-sm btn-outline-warning" onclick="unlockFbaReport('${report.month}', '${monthDisplay}')">🔓 Unlock</button>` :
                    `<button class="btn btn-sm btn-outline-danger" onclick="lockFbaReport('${report.month}', '${monthDisplay}')">🔒 Lock</button>`;

                tableHtml += `
                    <tr>
                        <td style="text-align: right;"><strong>${monthDisplay}</strong></td>
                        <td style="text-align: right;">${units}</td>
                        <td style="text-align: right;">${avgCost}</td>
                        <td style="text-align: right;">${eomValue}</td>
                        <td style="text-align: right;">${unitsNew}</td>
                        <td style="text-align: right;">${avgCostNew}</td>
                        <td style="text-align: right;">${eomValueNew}</td>
                        <td style="text-align: center;">${lockIcon}</td>
                        <td style="text-align: right;">${lockedAtDisplay}</td>
                        <td style="text-align: center;">${actionButton}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = tableHtml;
        }

        function lockFbaReport(month, monthDisplay) {
            fetch('/api/lock-fba-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ month: month })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    refreshFbaLocksList(); // Refresh the table to show updated lock status
                } else {
                    alert(`Failed to lock report: ${data.error}`);
                }
            })
            .catch(error => {
                alert(`Error locking report: ${error.message}`);
            });
        }

        function unlockFbaReport(month, monthDisplay) {
            if (!confirm(`⚠️ WARNING: Are you sure you want to UNLOCK the report for ${monthDisplay}?\n\nThis will allow the main columns to be overwritten during future report generations.\n\nAny locked historical data will become modifiable again.`)) {
                return;
            }

            // Double confirmation for unlock
            if (!confirm(`🚨 FINAL CONFIRMATION 🚨\n\nUnlocking ${monthDisplay} will make this historical data modifiable.\n\nAre you absolutely sure you want to proceed?`)) {
                return;
            }

            fetch('/api/unlock-fba-report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ month: month })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Successfully unlocked report for ${monthDisplay}`);
                    refreshFbaLocksList(); // Refresh the table
                } else {
                    alert(`Failed to unlock report: ${data.error}`);
                }
            })
            .catch(error => {
                alert(`Error unlocking report: ${error.message}`);
            });
        }

        // Initialize Informed tab
        document.addEventListener('DOMContentLoaded', function() {
            // Initial status refresh for Informed tab
            refreshInformedStatus();
        });
    </script>
</body>
</html>

