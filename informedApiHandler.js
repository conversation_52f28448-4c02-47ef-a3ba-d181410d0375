/**
 * Informed API Handler
 *
 * This module provides API endpoints for the Informed process.
 * It is imported by adminServer.js to handle Informed-related requests.
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { importAllReports, importReportByType } from './informedDirectImporter.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the Informed modules
let informedReportDownloader = null;
let informedReportImporter = null;
let informedScheduler = null;

// Initialize the Informed modules
async function initializeInformedModules() {
    try {
        // Use dynamic import for CommonJS modules
        const reportDownloaderModule = await import('./informedReportDownloader.cjs');
        const reportImporterModule = await import('./informedReportImporter.cjs');
        const schedulerModule = await import('./informedScheduler.cjs');

        informedReportDownloader = reportDownloaderModule.default || reportDownloaderModule;
        informedReportImporter = reportImporterModule.default || reportImporterModule;
        informedScheduler = schedulerModule.default || schedulerModule;

        // Initialize the scheduler
        informedScheduler.initialize();

        console.log('[informedApiHandler] Informed modules initialized successfully');
    } catch (error) {
        console.error('[informedApiHandler] Error initializing Informed modules:', error);
    }
}

// Initialize the modules when this file is imported
initializeInformedModules();

/**
 * Register Informed API endpoints with the Express app
 * @param {Object} app - Express app
 */
export function registerInformedApiEndpoints(app) {
    // API endpoint to get Informed report status
    app.get('/api/informed/status', async (req, res) => {
        try {
            if (!informedReportDownloader) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            const reports = await informedReportDownloader.getReportsStatus();

            res.json({
                success: true,
                reports
            });
        } catch (error) {
            console.error('[informedApiHandler] Error getting Informed report status:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to download Informed reports
    app.post('/api/informed/download-reports', async (req, res) => {
        try {
            if (!informedReportDownloader) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            // Get retry parameters from request or use defaults
            const maxRetries = req.body.maxRetries || 30;
            const retryInterval = req.body.retryInterval || 10000; // 10 seconds

            console.log(`[informedApiHandler] Downloading Informed reports (maxRetries: ${maxRetries}, retryInterval: ${retryInterval}ms)`);
            const results = await informedReportDownloader.downloadAllReports(maxRetries, retryInterval);

            res.json({
                success: true,
                reports: results
            });
        } catch (error) {
            console.error('[informedApiHandler] Error downloading Informed reports:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to import Informed reports
    app.post('/api/informed/import-reports', async (req, res) => {
        try {
            console.log('[informedApiHandler] Importing Informed reports using direct importer');

            // Use the direct importer to import the reports
            const results = await importAllReports();

            // Check if all imports were successful
            const allSuccessful = results.every(result => result.success);

            if (allSuccessful) {
                console.log('[informedApiHandler] All imports completed successfully');
                res.json({
                    success: true,
                    message: 'All imports completed successfully',
                    results
                });
            } else {
                const failedReports = results.filter(result => !result.success);
                console.error(`[informedApiHandler] ${failedReports.length} imports failed`);
                res.status(500).json({
                    success: false,
                    error: `${failedReports.length} imports failed`,
                    results
                });
            }
        } catch (error) {
            console.error('[informedApiHandler] Error importing Informed reports:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error',
                details: error.stack || ''
            });
        }
    });

    // API endpoint to run the full Informed process
    app.post('/api/informed/run-full-process', async (req, res) => {
        try {
            if (!informedReportDownloader) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            // Get retry parameters from request or use defaults
            const maxRetries = req.body.maxRetries || 30;
            const retryInterval = req.body.retryInterval || 10000; // 10 seconds

            console.log(`[informedApiHandler] Running full Informed process (maxRetries: ${maxRetries}, retryInterval: ${retryInterval}ms)`);

            // Step 1: Download reports
            console.log('[informedApiHandler] Step 1: Downloading reports');
            const downloadResults = await informedReportDownloader.downloadAllReports(maxRetries, retryInterval);

            // Check if download was successful
            const downloadSuccessful = downloadResults.every(result => result.success);
            if (!downloadSuccessful) {
                const failedReports = downloadResults.filter(result => !result.success);
                console.error(`[informedApiHandler] Failed to download ${failedReports.length} reports`);
                return res.json({
                    success: false,
                    message: `Failed to download ${failedReports.length} reports`,
                    downloadResults,
                    importResults: null
                });
            }

            // Step 2: Import reports
            console.log('[informedApiHandler] Step 2: Importing reports');
            const importResults = await importAllReports();

            // Check if all imports were successful
            const importSuccessful = importResults.every(result => result.success);

            // Return combined results
            res.json({
                success: downloadSuccessful && importSuccessful,
                message: `Download: ${downloadSuccessful ? 'Success' : 'Failed'}, Import: ${importSuccessful ? 'Success' : 'Failed'}`,
                downloadResults,
                importResults
            });
        } catch (error) {
            console.error('[informedApiHandler] Error running full Informed process:', error);
            res.status(500).json({
                success: false,
                error: error.message || 'Unknown error',
                details: error.stack || ''
            });
        }
    });

    // API endpoint to get scheduler status
    app.get('/api/informed/scheduler-status', (req, res) => {
        try {
            if (!informedScheduler) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            const status = informedScheduler.getSchedulerStatus();

            res.json({
                success: true,
                ...status
            });
        } catch (error) {
            console.error('[informedApiHandler] Error getting scheduler status:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to enable scheduler
    app.post('/api/informed/enable-scheduler', (req, res) => {
        try {
            if (!informedScheduler) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            console.log('[informedApiHandler] Enabling Informed scheduler');
            const result = informedScheduler.startScheduler();

            res.json(result);
        } catch (error) {
            console.error('[informedApiHandler] Error enabling scheduler:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    // API endpoint to disable scheduler
    app.post('/api/informed/disable-scheduler', (req, res) => {
        try {
            if (!informedScheduler) {
                return res.status(500).json({
                    success: false,
                    error: 'Informed modules not initialized'
                });
            }

            console.log('[informedApiHandler] Disabling Informed scheduler');
            const result = informedScheduler.stopScheduler();

            res.json(result);
        } catch (error) {
            console.error('[informedApiHandler] Error disabling scheduler:', error);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    });

    console.log('[informedApiHandler] Informed API endpoints registered');
}
