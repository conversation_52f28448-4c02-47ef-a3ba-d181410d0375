{"type": "module", "scripts": {"start-image-api": "node imageApi.js", "start-admin": "node adminServer.js", "start-worker": "node taskQueueWorker.js", "start-informed": "node informedScheduler.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.812.0", "@supabase/supabase-js": "^2.49.4", "axios": "^1.7.9", "cheerio": "^1.0.0", "chokidar": "^4.0.3", "cors": "^2.8.5", "cron-parser": "^4.9.0", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "dbffile": "^1.12.0", "dotenv": "^16.5.0", "express": "^4.21.2", "mime-types": "^2.1.35", "minimist": "^1.2.8", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "p-limit": "^6.2.0", "papaparse": "^5.5.1", "pdf-lib": "^1.17.1", "pg": "^8.13.3", "qrcode-terminal": "^0.12.0", "xlsx": "^0.18.5", "yargs": "^17.7.2"}}