// googleSheetsImporter.js - Import disc data from Google Sheets to t_discs table

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fetch from 'node-fetch';
import { parse } from 'csv-parse/sync';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Convert Google Sheets URL to CSV export URL
 * @param {string} sheetsUrl - The Google Sheets URL
 * @returns {string} - The CSV export URL
 */
function convertToCSVUrl(sheetsUrl) {
    // Extract the spreadsheet ID from the URL
    const match = sheetsUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    if (!match) {
        throw new Error('Invalid Google Sheets URL format');
    }

    const spreadsheetId = match[1];

    // Extract gid if present
    const gidMatch = sheetsUrl.match(/[#&]gid=([0-9]+)/);
    const gid = gidMatch ? gidMatch[1] : '0';

    return `https://docs.google.com/spreadsheets/d/${spreadsheetId}/export?format=csv&gid=${gid}`;
}

/**
 * Fetch data from Google Sheets as CSV
 * @param {string} sheetsUrl - The Google Sheets URL
 * @returns {Array} - Array of row objects
 */
async function fetchGoogleSheetsData(sheetsUrl) {
    const csvUrl = convertToCSVUrl(sheetsUrl);

    const response = await fetch(csvUrl);
    if (!response.ok) {
        throw new Error(`Failed to fetch Google Sheets data: ${response.status} ${response.statusText}`);
    }

    const csvData = await response.text();

    // Parse CSV data
    const records = parse(csvData, {
        columns: true,
        skip_empty_lines: true,
        trim: true
    });

    return records;
}

/**
 * Check if a record is empty (all required fields are empty)
 * @param {Object} record - The record to check
 * @returns {boolean} - True if the record is empty
 */
function isEmptyRecord(record) {
    return !record.shipment_id && !record.mps_id && !record.color;
}

/**
 * Validate a single disc record
 * @param {Object} record - The disc record to validate
 * @param {number} rowIndex - The row index for error reporting
 * @param {Object} lookupData - Lookup data for validation
 * @returns {Object} - Validation result
 */
async function validateDiscRecord(record, rowIndex, lookupData) {
    const errors = [];
    const warnings = [];

    // Skip empty records
    if (isEmptyRecord(record)) {
        return {
            isValid: false,
            isEmpty: true,
            errors: [],
            warnings: [],
            row: rowIndex + 2
        };
    }

    // Check required fields
    if (!record.shipment_id || record.shipment_id.toString().trim() === '') {
        errors.push('shipment_id is required');
    } else {
        const shipmentId = parseInt(record.shipment_id.toString().trim());
        if (isNaN(shipmentId)) {
            errors.push(`shipment_id '${record.shipment_id}' is not a valid number`);
        } else if (!lookupData.shipments.has(shipmentId)) {
            console.log(`DEBUG: shipment_id ${shipmentId} not found in set of ${lookupData.shipments.size} shipments`);
            console.log('DEBUG: Checking if shipment exists:', lookupData.shipments.has(shipmentId));
            console.log('DEBUG: Sample shipments:', Array.from(lookupData.shipments).slice(0, 10));
            errors.push(`shipment_id ${shipmentId} does not exist in t_shipments`);
        }
    }

    if (!record.mps_id || record.mps_id.toString().trim() === '') {
        errors.push('mps_id is required');
    } else {
        const mpsId = parseInt(record.mps_id.toString().trim());
        if (isNaN(mpsId)) {
            errors.push(`mps_id '${record.mps_id}' is not a valid number`);
        } else if (!lookupData.mps.has(mpsId)) {
            console.log(`DEBUG: mps_id ${mpsId} not found in set of ${lookupData.mps.size} MPS records`);
            console.log('DEBUG: Checking if MPS exists:', lookupData.mps.has(mpsId));
            console.log('DEBUG: Sample MPS IDs:', Array.from(lookupData.mps).slice(0, 10));
            errors.push(`mps_id ${mpsId} does not exist in t_mps`);
        }
    }

    if (!record.color || record.color.toString().trim() === '') {
        errors.push('color is required');
    } else {
        const colorName = record.color.toString().trim().toLowerCase();
        const colorId = lookupData.colors.get(colorName);
        if (!colorId) {
            errors.push(`color '${record.color}' does not exist in t_colors`);
        }
    }

    // Validate data types and constraints
    if (record.weight) {
        const weight = parseFloat(record.weight);
        if (isNaN(weight) || weight <= 0) {
            errors.push('weight must be a positive number');
        }
    }

    if (record.grade) {
        const grade = parseInt(record.grade);
        if (isNaN(grade) || grade < -10 || grade > 10) {
            errors.push('grade must be an integer between -10 and 10');
        }
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        row: rowIndex + 2 // +2 because CSV parsing starts at 0 and row 1 is header
    };
}

/**
 * Load lookup data for validation
 * @returns {Object} - Lookup data maps
 */
async function loadLookupData() {
    console.log('Loading shipments...');
    // Load shipments - get ALL records with explicit range to bypass default limits
    let allShipments = [];
    let from = 0;
    const pageSize = 1000;

    while (true) {
        const { data: shipments, error: shipmentsError } = await supabase
            .from('t_shipments')
            .select('id')
            .order('id')
            .range(from, from + pageSize - 1);

        if (shipmentsError) {
            throw new Error(`Failed to load shipments: ${shipmentsError.message}`);
        }

        if (!shipments || shipments.length === 0) break;

        allShipments = allShipments.concat(shipments);

        if (shipments.length < pageSize) break; // Last page
        from += pageSize;
    }

    console.log(`Loaded ${allShipments.length} shipments. Range: ${allShipments[0]?.id} to ${allShipments[allShipments.length-1]?.id}`);

    console.log('Loading MPS records...');
    // Load MPS records - get ALL records with explicit range
    let allMps = [];
    from = 0;

    while (true) {
        const { data: mps, error: mpsError } = await supabase
            .from('t_mps')
            .select('id')
            .order('id')
            .range(from, from + pageSize - 1);

        if (mpsError) {
            throw new Error(`Failed to load MPS records: ${mpsError.message}`);
        }

        if (!mps || mps.length === 0) break;

        allMps = allMps.concat(mps);

        if (mps.length < pageSize) break; // Last page
        from += pageSize;
    }

    console.log(`Loaded ${allMps.length} MPS records. Range: ${allMps[0]?.id} to ${allMps[allMps.length-1]?.id}`);

    console.log('Loading colors...');
    // Load colors (should be small enough to not need pagination)
    const { data: colors, error: colorsError } = await supabase
        .from('t_colors')
        .select('id, color')
        .order('id');

    if (colorsError) {
        throw new Error(`Failed to load colors: ${colorsError.message}`);
    }
    console.log(`Loaded ${colors.length} colors:`, colors.map(c => c.color).join(', '));

    const lookupData = {
        shipments: new Set(allShipments.map(s => s.id)),
        mps: new Set(allMps.map(m => m.id)),
        colors: new Map(colors.map(c => [c.color.toLowerCase(), c.id]))
    };

    // Verify the sets contain the expected data
    console.log('Shipment Set size:', lookupData.shipments.size);
    console.log('MPS Set size:', lookupData.mps.size);
    console.log('Colors Map size:', lookupData.colors.size);

    // Check if our test IDs are in the sets
    console.log('DEBUG: Does shipment 1992 exist?', lookupData.shipments.has(1992));
    console.log('DEBUG: Does shipment 1997 exist?', lookupData.shipments.has(1997));
    console.log('DEBUG: Does MPS 19214 exist?', lookupData.mps.has(19214));
    console.log('DEBUG: Does MPS 19652 exist?', lookupData.mps.has(19652));

    return lookupData;
}

/**
 * Transform validated record for database insertion
 * @param {Object} record - The original record
 * @param {Object} lookupData - Lookup data for mapping
 * @returns {Object} - Transformed record for database
 */
function transformRecordForDB(record, lookupData) {
    const transformed = {
        shipment_id: parseInt(record.shipment_id),
        mps_id: parseInt(record.mps_id),
        color_id: lookupData.colors.get(record.color.toLowerCase()),
        created_by: 'google_sheets_import'
    };

    // Add optional fields if present
    if (record.weight) transformed.weight = parseFloat(record.weight);
    if (record.grade) transformed.grade = parseInt(record.grade);
    if (record.color_modifier) transformed.color_modifier = record.color_modifier;
    if (record.description) transformed.description = record.description;
    if (record.notes) transformed.notes = record.notes;
    if (record.location) transformed.location = record.location;

    return transformed;
}

/**
 * Main import function
 * @param {string} googleSheetsUrl - The Google Sheets URL
 * @param {boolean} validateOnly - If true, only validate without importing
 * @returns {Object} - Import results
 */
export async function importDiscsFromGoogleSheets(googleSheetsUrl, validateOnly = false) {
    try {
        console.log('Fetching data from Google Sheets...');
        const records = await fetchGoogleSheetsData(googleSheetsUrl);

        console.log('Loading lookup data...');
        const lookupData = await loadLookupData();



        console.log('Validating records...');
        const validationResults = {
            totalRecords: records.length,
            validRecords: 0,
            invalidRecords: 0,
            emptyRecords: 0,
            errors: []
        };

        const validRecords = [];

        for (let i = 0; i < records.length; i++) {
            const validation = await validateDiscRecord(records[i], i, lookupData);

            if (validation.isEmpty) {
                validationResults.emptyRecords++;
                // Skip empty records - don't count as invalid
                continue;
            } else if (validation.isValid) {
                validationResults.validRecords++;
                validRecords.push(records[i]);
            } else {
                validationResults.invalidRecords++;
                validation.errors.forEach(error => {
                    validationResults.errors.push({
                        row: validation.row,
                        message: error
                    });
                });
            }
        }

        const result = {
            success: true,
            message: validateOnly ? 'Validation completed' : 'Import completed',
            validationResults
        };

        // If validation only, return early
        if (validateOnly) {
            return result;
        }

        // Import valid records
        if (validRecords.length > 0) {
            console.log(`Importing ${validRecords.length} valid records...`);

            const transformedRecords = validRecords.map(record =>
                transformRecordForDB(record, lookupData)
            );

            const { data: insertedRecords, error: insertError } = await supabase
                .from('t_discs')
                .insert(transformedRecords)
                .select('id');

            if (insertError) {
                throw new Error(`Failed to insert records: ${insertError.message}`);
            }

            result.importResults = {
                recordsImported: insertedRecords.length,
                newIds: insertedRecords.map(r => r.id)
            };
        } else {
            result.importResults = {
                recordsImported: 0,
                newIds: []
            };
        }

        return result;

    } catch (error) {
        console.error('Import error:', error);
        return {
            success: false,
            error: error.message,
            details: error.stack
        };
    }
}

export default importDiscsFromGoogleSheets;
